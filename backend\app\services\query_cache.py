"""
Query Caching Service - Phase 5 Advanced Features
Implements intelligent caching for query results with TTL and invalidation
"""

import json
import hashlib
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import structlog
from pathlib import Path

logger = structlog.get_logger()


class QueryCache:
    """
    Query caching service with file-based storage (Redis alternative)
    
    Features:
    - Query result caching with TTL
    - Cache invalidation
    - Cache statistics
    - Memory-efficient storage
    """
    
    def __init__(self, cache_dir: str = "cache", default_ttl_hours: int = 24):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.default_ttl_hours = default_ttl_hours
        self.stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "invalidations": 0
        }
        
        # Create subdirectories
        (self.cache_dir / "queries").mkdir(exist_ok=True)
        (self.cache_dir / "metadata").mkdir(exist_ok=True)
        
        logger.info("Query cache initialized", cache_dir=str(self.cache_dir))
    
    def _generate_cache_key(self, query: str, filters: Optional[Dict[str, Any]] = None) -> str:
        """Generate cache key from query and filters"""
        cache_data = {
            "query": query.lower().strip(),
            "filters": filters or {}
        }
        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    def _get_cache_path(self, cache_key: str) -> Path:
        """Get cache file path"""
        return self.cache_dir / "queries" / f"{cache_key}.json"
    
    def _get_metadata_path(self, cache_key: str) -> Path:
        """Get metadata file path"""
        return self.cache_dir / "metadata" / f"{cache_key}.json"
    
    def _is_expired(self, metadata: Dict[str, Any]) -> bool:
        """Check if cache entry is expired"""
        try:
            expires_at = datetime.fromisoformat(metadata["expires_at"])
            return datetime.now() > expires_at
        except (KeyError, ValueError):
            return True
    
    async def get(self, query: str, filters: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        Get cached query result
        
        Args:
            query: The query string
            filters: Optional query filters
            
        Returns:
            Cached result or None if not found/expired
        """
        try:
            cache_key = self._generate_cache_key(query, filters)
            cache_path = self._get_cache_path(cache_key)
            metadata_path = self._get_metadata_path(cache_key)
            
            # Check if cache files exist
            if not cache_path.exists() or not metadata_path.exists():
                self.stats["misses"] += 1
                return None
            
            # Load metadata
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            
            # Check expiration
            if self._is_expired(metadata):
                # Clean up expired cache
                cache_path.unlink(missing_ok=True)
                metadata_path.unlink(missing_ok=True)
                self.stats["misses"] += 1
                return None
            
            # Load cached result
            with open(cache_path, 'r') as f:
                cached_result = json.load(f)
            
            # Update access time
            metadata["last_accessed"] = datetime.now().isoformat()
            metadata["access_count"] = metadata.get("access_count", 0) + 1
            
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f)
            
            self.stats["hits"] += 1
            
            logger.info("Cache hit", cache_key=cache_key, access_count=metadata["access_count"])
            return cached_result
            
        except Exception as e:
            logger.error("Cache get failed", error=str(e))
            self.stats["misses"] += 1
            return None
    
    async def set(self, query: str, result: Dict[str, Any], 
                  filters: Optional[Dict[str, Any]] = None,
                  ttl_hours: Optional[int] = None) -> bool:
        """
        Cache query result
        
        Args:
            query: The query string
            result: Query result to cache
            filters: Optional query filters
            ttl_hours: Time to live in hours
            
        Returns:
            True if cached successfully
        """
        try:
            cache_key = self._generate_cache_key(query, filters)
            cache_path = self._get_cache_path(cache_key)
            metadata_path = self._get_metadata_path(cache_key)
            
            ttl = ttl_hours or self.default_ttl_hours
            expires_at = datetime.now() + timedelta(hours=ttl)
            
            # Create metadata
            metadata = {
                "query": query,
                "filters": filters,
                "cached_at": datetime.now().isoformat(),
                "expires_at": expires_at.isoformat(),
                "last_accessed": datetime.now().isoformat(),
                "access_count": 0,
                "ttl_hours": ttl,
                "result_size": len(json.dumps(result))
            }
            
            # Save result and metadata
            with open(cache_path, 'w') as f:
                json.dump(result, f)
            
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f)
            
            self.stats["sets"] += 1
            
            logger.info("Query cached", cache_key=cache_key, ttl_hours=ttl)
            return True
            
        except Exception as e:
            logger.error("Cache set failed", error=str(e))
            return False
    
    async def invalidate(self, query: str, filters: Optional[Dict[str, Any]] = None) -> bool:
        """
        Invalidate cached query result
        
        Args:
            query: The query string
            filters: Optional query filters
            
        Returns:
            True if invalidated successfully
        """
        try:
            cache_key = self._generate_cache_key(query, filters)
            cache_path = self._get_cache_path(cache_key)
            metadata_path = self._get_metadata_path(cache_key)
            
            # Remove cache files
            cache_path.unlink(missing_ok=True)
            metadata_path.unlink(missing_ok=True)
            
            self.stats["invalidations"] += 1
            
            logger.info("Cache invalidated", cache_key=cache_key)
            return True
            
        except Exception as e:
            logger.error("Cache invalidation failed", error=str(e))
            return False
    
    async def clear_expired(self) -> int:
        """
        Clear all expired cache entries
        
        Returns:
            Number of entries cleared
        """
        cleared_count = 0
        
        try:
            metadata_dir = self.cache_dir / "metadata"
            
            for metadata_path in metadata_dir.glob("*.json"):
                try:
                    with open(metadata_path, 'r') as f:
                        metadata = json.load(f)
                    
                    if self._is_expired(metadata):
                        cache_key = metadata_path.stem
                        cache_path = self._get_cache_path(cache_key)
                        
                        # Remove both files
                        cache_path.unlink(missing_ok=True)
                        metadata_path.unlink(missing_ok=True)
                        
                        cleared_count += 1
                        
                except Exception as e:
                    logger.error("Failed to check cache entry", file=str(metadata_path), error=str(e))
            
            logger.info("Expired cache entries cleared", count=cleared_count)
            return cleared_count
            
        except Exception as e:
            logger.error("Cache cleanup failed", error=str(e))
            return 0
    
    async def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics
        
        Returns:
            Cache statistics including hit rate, size, etc.
        """
        try:
            # Count cache files
            queries_dir = self.cache_dir / "queries"
            metadata_dir = self.cache_dir / "metadata"
            
            total_entries = len(list(queries_dir.glob("*.json")))
            total_size = sum(f.stat().st_size for f in queries_dir.glob("*.json"))
            
            # Calculate hit rate
            total_requests = self.stats["hits"] + self.stats["misses"]
            hit_rate = (self.stats["hits"] / total_requests * 100) if total_requests > 0 else 0
            
            # Get recent entries info
            recent_entries = []
            for metadata_path in sorted(metadata_dir.glob("*.json"), 
                                      key=lambda p: p.stat().st_mtime, reverse=True)[:5]:
                try:
                    with open(metadata_path, 'r') as f:
                        metadata = json.load(f)
                    recent_entries.append({
                        "query": metadata["query"][:100] + "..." if len(metadata["query"]) > 100 else metadata["query"],
                        "cached_at": metadata["cached_at"],
                        "access_count": metadata.get("access_count", 0),
                        "expires_at": metadata["expires_at"]
                    })
                except:
                    continue
            
            return {
                "total_entries": total_entries,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / 1024 / 1024, 2),
                "hit_rate_percent": round(hit_rate, 2),
                "stats": self.stats.copy(),
                "recent_entries": recent_entries,
                "cache_dir": str(self.cache_dir)
            }
            
        except Exception as e:
            logger.error("Failed to get cache stats", error=str(e))
            return {"error": str(e)}
    
    async def clear_all(self) -> bool:
        """
        Clear all cache entries
        
        Returns:
            True if cleared successfully
        """
        try:
            # Remove all cache files
            for cache_file in (self.cache_dir / "queries").glob("*.json"):
                cache_file.unlink()
            
            for metadata_file in (self.cache_dir / "metadata").glob("*.json"):
                metadata_file.unlink()
            
            # Reset stats
            self.stats = {
                "hits": 0,
                "misses": 0,
                "sets": 0,
                "invalidations": 0
            }
            
            logger.info("All cache entries cleared")
            return True
            
        except Exception as e:
            logger.error("Failed to clear cache", error=str(e))
            return False


# Global cache instance
query_cache = QueryCache()
