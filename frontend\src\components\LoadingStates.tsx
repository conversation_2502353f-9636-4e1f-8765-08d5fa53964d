/**
 * Loading States and Error Handling Components
 * Phase 6: Frontend Development - Task 6.1
 */

import React from 'react';
import {
  Box,
  Paper,
  Typography,
  CircularProgress,
  LinearProgress,
  Skeleton,
  Alert,
  Button,
  Card,
  CardContent,
  Fade,
  Grow,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Search as SearchIcon,
  CloudOff as OfflineIcon,
} from '@mui/icons-material';

// Query Loading Component
export const QueryLoading: React.FC<{ message?: string }> = ({ 
  message = "Processing your query..." 
}) => (
  <Fade in>
    <Paper elevation={2} sx={{ p: 4, textAlign: 'center', mb: 3 }}>
      <Box display="flex" flexDirection="column" alignItems="center" gap={3}>
        <CircularProgress size={60} thickness={4} />
        
        <Box>
          <Typography variant="h6" gutterBottom>
            {message}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            This may take a few moments while we search through SEC filings...
          </Typography>
        </Box>

        {/* Progress Steps */}
        <Box width="100%" maxWidth={400}>
          <Box display="flex" justifyContent="space-between" mb={1}>
            <Typography variant="caption">Analyzing query</Typography>
            <Typography variant="caption">Generating response</Typography>
          </Box>
          <LinearProgress />
        </Box>
      </Box>
    </Paper>
  </Fade>
);

// Results Loading Skeleton
export const ResultsSkeleton: React.FC = () => (
  <Box>
    {/* Answer Skeleton */}
    <Paper elevation={2} sx={{ p: 4, mb: 3 }}>
      <Skeleton variant="text" width="30%" height={40} sx={{ mb: 2 }} />
      <Skeleton variant="text" width="100%" height={24} sx={{ mb: 1 }} />
      <Skeleton variant="text" width="100%" height={24} sx={{ mb: 1 }} />
      <Skeleton variant="text" width="80%" height={24} sx={{ mb: 3 }} />
      
      <Box display="flex" gap={1}>
        <Skeleton variant="rounded" width={100} height={32} />
        <Skeleton variant="rounded" width={80} height={32} />
        <Skeleton variant="rounded" width={120} height={32} />
      </Box>
    </Paper>

    {/* Sources Skeleton */}
    <Paper elevation={2} sx={{ p: 3 }}>
      <Skeleton variant="text" width="20%" height={32} sx={{ mb: 2 }} />
      
      {[1, 2, 3].map((i) => (
        <Card key={i} variant="outlined" sx={{ mb: 2 }}>
          <CardContent>
            <Skeleton variant="text" width="40%" height={24} sx={{ mb: 1 }} />
            <Box display="flex" gap={1} mb={2}>
              <Skeleton variant="rounded" width={80} height={24} />
              <Skeleton variant="rounded" width={100} height={24} />
              <Skeleton variant="rounded" width={90} height={24} />
            </Box>
            <Skeleton variant="text" width="100%" height={20} />
            <Skeleton variant="text" width="60%" height={20} />
          </CardContent>
        </Card>
      ))}
    </Paper>
  </Box>
);

// Companies Loading Skeleton
export const CompaniesLoading: React.FC = () => (
  <Box>
    {[1, 2, 3, 4, 5].map((i) => (
      <Card key={i} sx={{ mb: 2 }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start">
            <Box flexGrow={1}>
              <Skeleton variant="text" width="30%" height={28} sx={{ mb: 1 }} />
              <Skeleton variant="text" width="60%" height={20} sx={{ mb: 2 }} />
              
              <Box display="flex" gap={1} mb={1}>
                <Skeleton variant="rounded" width={60} height={20} />
                <Skeleton variant="rounded" width={80} height={20} />
                <Skeleton variant="rounded" width={70} height={20} />
              </Box>
            </Box>
            
            <Skeleton variant="rounded" width={100} height={36} />
          </Box>
        </CardContent>
      </Card>
    ))}
  </Box>
);

// Error Display Component
interface ErrorDisplayProps {
  error: string;
  onRetry?: () => void;
  type?: 'error' | 'warning' | 'info';
  title?: string;
  showRetry?: boolean;
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  onRetry,
  type = 'error',
  title,
  showRetry = true,
}) => {
  const getIcon = () => {
    switch (type) {
      case 'warning': return <WarningIcon />;
      case 'info': return <InfoIcon />;
      default: return <ErrorIcon />;
    }
  };

  const getTitle = () => {
    if (title) return title;
    switch (type) {
      case 'warning': return 'Warning';
      case 'info': return 'Information';
      default: return 'Error';
    }
  };

  return (
    <Grow in>
      <Paper elevation={2} sx={{ p: 4, mb: 3 }}>
        <Alert 
          severity={type}
          icon={getIcon()}
          action={
            showRetry && onRetry && (
              <Button
                color="inherit"
                size="small"
                onClick={onRetry}
                startIcon={<RefreshIcon />}
              >
                Retry
              </Button>
            )
          }
        >
          <Typography variant="h6" gutterBottom>
            {getTitle()}
          </Typography>
          <Typography variant="body2">
            {error}
          </Typography>
        </Alert>
      </Paper>
    </Grow>
  );
};

// Network Error Component
export const NetworkError: React.FC<{ onRetry?: () => void }> = ({ onRetry }) => (
  <ErrorDisplay
    error="Unable to connect to the server. Please check your internet connection and try again."
    onRetry={onRetry}
    type="error"
    title="Connection Error"
  />
);

// API Error Component
export const APIError: React.FC<{ error: string; onRetry?: () => void }> = ({ 
  error, 
  onRetry 
}) => (
  <ErrorDisplay
    error={error}
    onRetry={onRetry}
    type="error"
    title="API Error"
  />
);

// No Results Component
export const NoResults: React.FC<{ 
  query?: string; 
  onClear?: () => void;
  suggestions?: string[];
}> = ({ 
  query, 
  onClear,
  suggestions = []
}) => (
  <Grow in>
    <Paper elevation={2} sx={{ p: 4, textAlign: 'center', mb: 3 }}>
      <SearchIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
      
      <Typography variant="h6" gutterBottom>
        No results found
      </Typography>
      
      {query && (
        <Typography variant="body2" color="text.secondary" gutterBottom>
          We couldn't find any information for: "{query}"
        </Typography>
      )}

      {suggestions.length > 0 && (
        <Box mt={3}>
          <Typography variant="subtitle2" gutterBottom>
            Try these suggestions:
          </Typography>
          <Box display="flex" flexDirection="column" gap={1} alignItems="center">
            {suggestions.map((suggestion, index) => (
              <Typography key={index} variant="body2" color="primary">
                • {suggestion}
              </Typography>
            ))}
          </Box>
        </Box>
      )}

      {onClear && (
        <Button
          variant="outlined"
          onClick={onClear}
          sx={{ mt: 3 }}
        >
          Clear Search
        </Button>
      )}
    </Paper>
  </Grow>
);

// Offline Indicator
export const OfflineIndicator: React.FC = () => (
  <Alert 
    severity="warning" 
    icon={<OfflineIcon />}
    sx={{ mb: 2 }}
  >
    <Typography variant="body2">
      You appear to be offline. Some features may not be available.
    </Typography>
  </Alert>
);

// Loading Overlay
export const LoadingOverlay: React.FC<{ 
  loading: boolean; 
  message?: string;
  children: React.ReactNode;
}> = ({ 
  loading, 
  message = "Loading...",
  children 
}) => (
  <Box position="relative">
    {children}
    
    {loading && (
      <Fade in>
        <Box
          position="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          display="flex"
          alignItems="center"
          justifyContent="center"
          bgcolor="rgba(255, 255, 255, 0.8)"
          zIndex={1000}
        >
          <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
            <CircularProgress />
            <Typography variant="body2" color="text.secondary">
              {message}
            </Typography>
          </Box>
        </Box>
      </Fade>
    )}
  </Box>
);

// Progress Indicator
export const ProgressIndicator: React.FC<{
  steps: string[];
  currentStep: number;
  completed?: boolean;
}> = ({ steps, currentStep, completed = false }) => (
  <Box width="100%" mb={3}>
    <Box display="flex" justifyContent="space-between" mb={1}>
      {steps.map((step, index) => (
        <Typography
          key={index}
          variant="caption"
          color={
            completed || index < currentStep 
              ? 'primary' 
              : index === currentStep 
                ? 'text.primary' 
                : 'text.secondary'
          }
          fontWeight={index === currentStep ? 'bold' : 'normal'}
        >
          {step}
        </Typography>
      ))}
    </Box>
    
    <LinearProgress
      variant={completed ? "determinate" : "indeterminate"}
      value={completed ? 100 : (currentStep / steps.length) * 100}
      color={completed ? "success" : "primary"}
    />
  </Box>
);

export default {
  QueryLoading,
  ResultsSkeleton,
  CompaniesLoading,
  ErrorDisplay,
  NetworkError,
  APIError,
  NoResults,
  OfflineIndicator,
  LoadingOverlay,
  ProgressIndicator,
};
