import asyncio
from app.services.sec_api import filing_manager
from app.services.document_parser import document_parser
from app.services.document_chunker import document_chunker

async def test_document_parser():
    """Test document parser with real SEC filing"""
    try:
        print("🔍 Testing Document Parser...")
        
        # Get a recent Apple filing
        print("  📊 Fetching Apple filings...")
        filings = await filing_manager.get_company_filings("AAPL", limit=3)
        
        if not filings:
            print("  ❌ No filings found for Apple")
            return False
        
        # Get the most recent filing
        latest_filing = filings[0]
        print(f"  📄 Latest filing: {latest_filing['form']} ({latest_filing['filing_date']})")
        
        # Get the filing content
        print("  📖 Fetching filing content...")
        try:
            content = await filing_manager.get_filing_content(
                latest_filing['ticker'],
                latest_filing['accession_number'],
                latest_filing['primary_document']
            )
            print(f"  ✅ Retrieved content ({len(content)} characters)")
        except Exception as e:
            print(f"  ⚠️  Could not fetch content: {str(e)}")
            # Use sample HTML for testing
            content = """
            <html>
            <head><title>Apple Inc. - Form 10-Q</title></head>
            <body>
            <h1>UNITED STATES SECURITIES AND EXCHANGE COMMISSION</h1>
            <h2>FORM 10-Q</h2>
            <p>Company Name: Apple Inc.</p>
            <p>CIK: 0000320193</p>
            <p>For the quarter ended June 30, 2023</p>
            
            <h3>PART I - FINANCIAL INFORMATION</h3>
            <h4>Item 1. Financial Statements</h4>
            <p>The following financial statements are included...</p>
            <table>
            <tr><th>Revenue</th><th>Q2 2023</th><th>Q1 2023</th></tr>
            <tr><td>Product Revenue</td><td>$60.6B</td><td>$66.8B</td></tr>
            <tr><td>Services Revenue</td><td>$21.2B</td><td>$20.9B</td></tr>
            </table>
            
            <h4>Item 2. Management's Discussion and Analysis</h4>
            <p>Management discusses the financial performance...</p>
            
            <h3>PART II - OTHER INFORMATION</h3>
            <h4>Item 1A. Risk Factors</h4>
            <p>The following risk factors may materially affect our business...</p>
            </body>
            </html>
            """
            print(f"  📝 Using sample content for testing ({len(content)} characters)")
        
        # Parse the document
        print("  🔧 Parsing document...")
        parsed_doc = document_parser.parse_document(
            html_content=content,
            filing_type=latest_filing['form'],
            ticker=latest_filing['ticker'],
            filing_date=latest_filing['filing_date']
        )
        
        # Display results
        print("  ✅ Document parsed successfully!")
        print(f"     📊 Sections found: {len(parsed_doc['sections'])}")
        print(f"     📋 Tables found: {len(parsed_doc['tables'])}")
        print(f"     📝 Text length: {len(parsed_doc['full_text'])} characters")
        
        # Show sections
        if parsed_doc['sections']:
            print("     📑 Sections:")
            for section_name in parsed_doc['sections'].keys():
                print(f"       - {section_name}")
        
        # Show metadata
        metadata = parsed_doc['metadata']
        print("     📋 Metadata:")
        print(f"       - Company: {metadata.get('company_name', 'N/A')}")
        print(f"       - CIK: {metadata.get('cik', 'N/A')}")
        print(f"       - Period End: {metadata.get('period_end_date', 'N/A')}")
        
        # Extract key information
        print("  🔑 Extracting key information...")
        key_info = document_parser.extract_key_information(parsed_doc)
        print(f"     📊 Summary: {key_info['summary']}")
        print(f"     ✅ Has Financial Data: {key_info['has_financial_data']}")
        print(f"     ⚠️  Has Risk Factors: {key_info['has_risk_factors']}")

        # Test document chunking
        print("  ✂️  Testing document chunking...")
        chunks = document_chunker.chunk_document(parsed_doc)
        print(f"     📦 Created {len(chunks)} chunks")

        if chunks:
            # Show chunk statistics
            chunk_summary = document_chunker.create_chunk_summary(chunks)
            print(f"     📊 Average chunk size: {chunk_summary['average_chunk_size']} characters")
            print(f"     💰 Financial chunks: {chunk_summary['financial_chunks']}/{chunk_summary['total_chunks']}")

            # Show first chunk details
            first_chunk = chunks[0]
            print(f"     📝 First chunk preview: {first_chunk['text'][:100]}...")
            print(f"     🏷️  First chunk metadata: {first_chunk['metadata']['section_name']}")

        return True
        
    except Exception as e:
        print(f"  ❌ Document parser test failed: {str(e)}")
        return False

async def test_section_extraction():
    """Test section extraction with different filing types"""
    print("\n🔍 Testing Section Extraction...")
    
    # Test data for different filing types
    test_cases = [
        {
            'filing_type': '10-K',
            'html': '''
            <html><body>
            <h1>FORM 10-K</h1>
            <h2>Item 1. Business</h2>
            <p>Apple Inc. designs, manufactures and markets smartphones, personal computers...</p>
            <h2>Item 1A. Risk Factors</h2>
            <p>The following risk factors may materially affect our business operations...</p>
            <h2>Item 2. Properties</h2>
            <p>Our corporate headquarters are located in Cupertino, California...</p>
            </body></html>
            '''
        },
        {
            'filing_type': '10-Q',
            'html': '''
            <html><body>
            <h1>FORM 10-Q</h1>
            <h2>PART I - FINANCIAL INFORMATION</h2>
            <h3>Item 1. Financial Statements</h3>
            <p>Condensed consolidated statements of operations...</p>
            <h3>Item 2. Management's Discussion and Analysis</h3>
            <p>The following discussion should be read in conjunction...</p>
            </body></html>
            '''
        }
    ]
    
    for i, test_case in enumerate(test_cases):
        try:
            print(f"  📄 Testing {test_case['filing_type']} parsing...")
            
            parsed_doc = document_parser.parse_document(
                html_content=test_case['html'],
                filing_type=test_case['filing_type'],
                ticker='TEST',
                filing_date='2023-01-01'
            )
            
            sections_found = len(parsed_doc['sections'])
            print(f"     ✅ Found {sections_found} sections")
            
            for section_name in parsed_doc['sections'].keys():
                print(f"       - {section_name}")
                
        except Exception as e:
            print(f"     ❌ Failed to parse {test_case['filing_type']}: {str(e)}")
    
    return True

async def test_document_chunking():
    """Test document chunking with various text sizes"""
    print("\n✂️  Testing Document Chunking...")

    try:
        # Create test documents of different sizes
        test_documents = [
            {
                'name': 'Small Document',
                'metadata': {
                    'ticker': 'TEST',
                    'filing_type': '10-K',
                    'filing_date': '2023-01-01',
                    'company_name': 'Test Company'
                },
                'sections': {
                    'business': 'This is a small business section. ' * 10
                },
                'full_text': 'This is a small business section. ' * 10
            },
            {
                'name': 'Large Document',
                'metadata': {
                    'ticker': 'TEST',
                    'filing_type': '10-K',
                    'filing_date': '2023-01-01',
                    'company_name': 'Test Company'
                },
                'sections': {
                    'business': 'This is a large business section with lots of content. ' * 100,
                    'risk_factors': 'These are the risk factors that may affect our business. ' * 50
                },
                'full_text': 'Combined content. ' * 200
            }
        ]

        for test_doc in test_documents:
            print(f"  📄 Testing {test_doc['name']}...")

            chunks = document_chunker.chunk_document(test_doc)
            print(f"     📦 Created {len(chunks)} chunks")

            if chunks:
                # Test chunk properties
                for i, chunk in enumerate(chunks[:2]):  # Show first 2 chunks
                    metadata = chunk['metadata']
                    print(f"     📝 Chunk {i+1}: {len(chunk['text'])} chars, section: {metadata['section_name']}")
                    print(f"        Key phrases: {metadata['key_phrases'][:3]}")
                    print(f"        Has financial data: {metadata['has_financial_data']}")

                # Test chunk summary
                summary = document_chunker.create_chunk_summary(chunks)
                print(f"     📊 Summary: {summary['total_chunks']} chunks, avg size: {summary['average_chunk_size']}")

        return True

    except Exception as e:
        print(f"  ❌ Document chunking test failed: {str(e)}")
        return False

async def main():
    """Main test function"""
    print("🚀 Document Parser Tests")
    print("=" * 40)
    
    # Run tests
    tests = [
        ("Document Parser", test_document_parser),
        ("Section Extraction", test_section_extraction),
        ("Document Chunking", test_document_chunking),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All document parser tests passed!")
        return 0
    else:
        print("💥 Some document parser tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
