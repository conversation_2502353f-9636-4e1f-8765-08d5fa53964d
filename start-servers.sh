#!/bin/bash
# Unix/Linux/macOS Shell Script to Start SEC Filing QA Agent Servers
# Starts both FastAPI backend and React frontend

set -e  # Exit on any error

echo ""
echo "========================================"
echo "  SEC Filing QA Agent - Server Startup"
echo "========================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

# Check if directories exist
if [ ! -d "backend" ]; then
    print_error "Backend directory not found!"
    echo "Please run this script from the project root directory."
    exit 1
fi

if [ ! -d "frontend" ]; then
    print_error "Frontend directory not found!"
    echo "Please run this script from the project root directory."
    exit 1
fi

echo "🔍 Checking prerequisites..."

# Check Python
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    print_error "Python not found! Please install Python 3.8+"
    exit 1
fi

# Use python3 if available, otherwise python
PYTHON_CMD="python3"
if ! command -v python3 &> /dev/null; then
    PYTHON_CMD="python"
fi

# Check Node.js/npm
if ! command -v npm &> /dev/null; then
    print_error "npm not found! Please install Node.js"
    exit 1
fi

print_status "Prerequisites check passed"
echo ""

# Function to cleanup background processes
cleanup() {
    echo ""
    print_info "Stopping servers..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    echo "👋 Servers stopped. Goodbye!"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Install backend dependencies if needed
echo "📦 Checking backend dependencies..."
cd backend

if [ ! -d "venv" ]; then
    echo "Creating Python virtual environment..."
    $PYTHON_CMD -m venv venv
fi

echo "Activating virtual environment..."
source venv/bin/activate

echo "Installing/updating backend dependencies..."
pip install -r requirements.txt > /dev/null 2>&1

cd ..

# Install frontend dependencies if needed
echo "📦 Checking frontend dependencies..."
cd frontend
if [ ! -d "node_modules" ]; then
    echo "Installing frontend dependencies..."
    npm install
fi
cd ..

echo ""
echo "🚀 Starting servers..."
echo ""

# Start backend server
echo "🔧 Starting Backend Server (FastAPI)..."
cd backend
source venv/bin/activate
$PYTHON_CMD -m uvicorn app.main:app --reload --host 127.0.0.1 --port 8000 > ../backend.log 2>&1 &
BACKEND_PID=$!
cd ..

# Wait a moment for backend to start
sleep 5

# Start frontend server
echo "🌐 Starting Frontend Server (React)..."
cd frontend
npm run dev > ../frontend.log 2>&1 &
FRONTEND_PID=$!
cd ..

# Wait for servers to start
echo "⏳ Waiting for servers to start..."
sleep 10

# Check if servers are running
echo ""
echo "🔍 Checking server status..."

# Check backend
if curl -s http://127.0.0.1:8000/api/v1/health/ > /dev/null 2>&1; then
    print_status "Backend Server: RUNNING (http://127.0.0.1:8000)"
else
    print_warning "Backend Server: Starting... (may take a moment)"
fi

# Check frontend (this might not work immediately)
if curl -s http://localhost:5173 > /dev/null 2>&1; then
    print_status "Frontend Server: RUNNING (http://localhost:5173)"
else
    print_warning "Frontend Server: Starting... (may take a moment)"
fi

echo ""
echo "🎉 SERVERS STARTED!"
echo "========================================"
echo "🌐 Frontend: http://localhost:5173"
echo "🔧 Backend:  http://127.0.0.1:8000"
echo "📚 API Docs: http://127.0.0.1:8000/docs"
echo "========================================"
echo ""
echo "💡 TESTING INSTRUCTIONS:"
echo "1. Open http://localhost:5173 in your browser"
echo "2. Try asking: 'What are Apple's main revenue sources?'"
echo "3. Test advanced features: history, bookmarks, export"
echo ""
echo "🧪 RUN QUICK TEST:"
echo "   python3 quick_test.py"
echo ""
echo "🧪 RUN FULL INTEGRATION TEST:"
echo "   python3 test_integration_complete.py"
echo ""
echo "📋 LOGS:"
echo "   Backend:  tail -f backend.log"
echo "   Frontend: tail -f frontend.log"
echo ""
echo "⚠️  Press Ctrl+C to stop both servers"
echo ""

# Keep script running and wait for interrupt
while true; do
    sleep 1
    
    # Check if processes are still running
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        print_error "Backend server stopped unexpectedly"
        break
    fi
    
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        print_error "Frontend server stopped unexpectedly"
        break
    fi
done

cleanup
