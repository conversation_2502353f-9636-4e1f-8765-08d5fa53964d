/**
 * Main App Component with Error Boundary and Context Integration
 * Phase 6: Frontend Development - Task 6.2
 */

import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Container, AppBar, Toolbar, Typography, Box, IconButton, Tooltip } from '@mui/material';
import {
  Search as SearchIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
} from '@mui/icons-material';

// Import context and error boundary
import { AppProvider, useAppTheme } from './contexts/AppContext';
import ErrorBoundary from './components/ErrorBoundary';

// Import pages
import HomePage from './pages/HomePage';
import './App.css';

// App Header Component with Theme Toggle
const AppHeader: React.FC = () => {
  const { theme, setTheme } = useAppTheme();

  const handleThemeToggle = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  return (
    <AppBar position="static">
      <Toolbar>
        <SearchIcon sx={{ mr: 2 }} />
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          SEC Filings QA Agent
        </Typography>

        <Tooltip title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}>
          <IconButton
            color="inherit"
            onClick={handleThemeToggle}
            edge="end"
          >
            {theme === 'light' ? <DarkModeIcon /> : <LightModeIcon />}
          </IconButton>
        </Tooltip>
      </Toolbar>
    </AppBar>
  );
};

// Main App Content
const AppContent: React.FC = () => {
  return (
    <Router>
      <Box sx={{ flexGrow: 1, minHeight: '100vh', bgcolor: 'background.default' }}>
        <ErrorBoundary>
          <AppHeader />
        </ErrorBoundary>

        <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
          <ErrorBoundary>
            <Routes>
              <Route path="/" element={<HomePage />} />
              {/* Add more routes here as needed */}
            </Routes>
          </ErrorBoundary>
        </Container>
      </Box>
    </Router>
  );
};

// Main App Component with Providers
function App() {
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Global error caught:', error, errorInfo);
        // In production, you might want to send this to an error reporting service
      }}
    >
      <AppProvider>
        <AppContent />
      </AppProvider>
    </ErrorBoundary>
  );
}

export default App;
