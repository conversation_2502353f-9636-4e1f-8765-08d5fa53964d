"""
Query History Service - Phase 5 Advanced Features
Tracks and manages user query history with analytics
"""

import json
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path
import structlog

logger = structlog.get_logger()


class QueryHistoryEntry:
    """Single query history entry"""
    
    def __init__(self, query: str, response: Dict[str, Any], 
                 user_id: Optional[str] = None, session_id: Optional[str] = None):
        self.id = str(uuid.uuid4())
        self.query = query
        self.response = response
        self.user_id = user_id or "anonymous"
        self.session_id = session_id or "default"
        self.timestamp = datetime.now().isoformat()
        self.processing_time = response.get("processing_time", 0)
        self.success = response.get("success", False)
        self.model_used = response.get("model_used")
        self.sources_count = len(response.get("sources", []))
        self.confidence = response.get("confidence")
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "id": self.id,
            "query": self.query,
            "response": self.response,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "timestamp": self.timestamp,
            "processing_time": self.processing_time,
            "success": self.success,
            "model_used": self.model_used,
            "sources_count": self.sources_count,
            "confidence": self.confidence
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'QueryHistoryEntry':
        """Create from dictionary"""
        entry = cls.__new__(cls)
        entry.id = data["id"]
        entry.query = data["query"]
        entry.response = data["response"]
        entry.user_id = data["user_id"]
        entry.session_id = data["session_id"]
        entry.timestamp = data["timestamp"]
        entry.processing_time = data["processing_time"]
        entry.success = data["success"]
        entry.model_used = data["model_used"]
        entry.sources_count = data["sources_count"]
        entry.confidence = data["confidence"]
        return entry


class QueryHistoryService:
    """
    Query history service with persistent storage and analytics
    
    Features:
    - Query history tracking
    - User session management
    - Query analytics and insights
    - Export functionality
    """
    
    def __init__(self, history_dir: str = "history", max_entries: int = 10000):
        self.history_dir = Path(history_dir)
        self.history_dir.mkdir(exist_ok=True)
        self.max_entries = max_entries
        self.history_file = self.history_dir / "query_history.jsonl"
        
        logger.info("Query history service initialized", 
                   history_dir=str(self.history_dir),
                   max_entries=max_entries)
    
    async def add_query(self, query: str, response: Dict[str, Any],
                       user_id: Optional[str] = None, 
                       session_id: Optional[str] = None) -> str:
        """
        Add query to history
        
        Args:
            query: The query string
            response: Query response
            user_id: Optional user identifier
            session_id: Optional session identifier
            
        Returns:
            Query entry ID
        """
        try:
            entry = QueryHistoryEntry(query, response, user_id, session_id)
            
            # Append to history file
            with open(self.history_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(entry.to_dict()) + '\n')
            
            # Cleanup if too many entries
            await self._cleanup_old_entries()
            
            logger.info("Query added to history", 
                       entry_id=entry.id,
                       user_id=entry.user_id,
                       success=entry.success)
            
            return entry.id
            
        except Exception as e:
            logger.error("Failed to add query to history", error=str(e))
            return ""
    
    async def get_history(self, user_id: Optional[str] = None,
                         session_id: Optional[str] = None,
                         limit: int = 50,
                         offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get query history with filtering
        
        Args:
            user_id: Filter by user ID
            session_id: Filter by session ID
            limit: Maximum number of entries
            offset: Number of entries to skip
            
        Returns:
            List of query history entries
        """
        try:
            if not self.history_file.exists():
                return []
            
            entries = []
            with open(self.history_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        entry_data = json.loads(line.strip())
                        
                        # Apply filters
                        if user_id and entry_data.get("user_id") != user_id:
                            continue
                        if session_id and entry_data.get("session_id") != session_id:
                            continue
                        
                        entries.append(entry_data)
                        
                    except json.JSONDecodeError:
                        continue
            
            # Sort by timestamp (newest first)
            entries.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
            
            # Apply pagination
            paginated_entries = entries[offset:offset + limit]
            
            logger.info("Retrieved query history",
                       total_entries=len(entries),
                       returned_entries=len(paginated_entries),
                       user_id=user_id,
                       session_id=session_id)
            
            return paginated_entries
            
        except Exception as e:
            logger.error("Failed to get query history", error=str(e))
            return []
    
    async def get_query_by_id(self, query_id: str) -> Optional[Dict[str, Any]]:
        """
        Get specific query by ID
        
        Args:
            query_id: Query entry ID
            
        Returns:
            Query entry or None if not found
        """
        try:
            if not self.history_file.exists():
                return None
            
            with open(self.history_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        entry_data = json.loads(line.strip())
                        if entry_data.get("id") == query_id:
                            return entry_data
                    except json.JSONDecodeError:
                        continue
            
            return None
            
        except Exception as e:
            logger.error("Failed to get query by ID", query_id=query_id, error=str(e))
            return None
    
    async def get_analytics(self, user_id: Optional[str] = None,
                           days: int = 30) -> Dict[str, Any]:
        """
        Get query analytics
        
        Args:
            user_id: Filter by user ID
            days: Number of days to analyze
            
        Returns:
            Analytics data
        """
        try:
            if not self.history_file.exists():
                return {"total_queries": 0}
            
            # Calculate date threshold
            from datetime import timedelta
            threshold_date = datetime.now() - timedelta(days=days)
            
            entries = []
            with open(self.history_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        entry_data = json.loads(line.strip())
                        
                        # Filter by user if specified
                        if user_id and entry_data.get("user_id") != user_id:
                            continue
                        
                        # Filter by date
                        entry_date = datetime.fromisoformat(entry_data.get("timestamp", ""))
                        if entry_date < threshold_date:
                            continue
                        
                        entries.append(entry_data)
                        
                    except (json.JSONDecodeError, ValueError):
                        continue
            
            # Calculate analytics
            total_queries = len(entries)
            successful_queries = sum(1 for e in entries if e.get("success", False))
            
            # Average processing time
            processing_times = [e.get("processing_time", 0) for e in entries if e.get("processing_time")]
            avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
            
            # Most used models
            models = [e.get("model_used") for e in entries if e.get("model_used")]
            model_counts = {}
            for model in models:
                model_counts[model] = model_counts.get(model, 0) + 1
            
            # Query patterns (top query words)
            all_queries = " ".join([e.get("query", "") for e in entries]).lower()
            words = all_queries.split()
            word_counts = {}
            for word in words:
                if len(word) > 3:  # Skip short words
                    word_counts[word] = word_counts.get(word, 0) + 1
            
            top_words = sorted(word_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            
            # Daily query counts
            daily_counts = {}
            for entry in entries:
                try:
                    date = datetime.fromisoformat(entry.get("timestamp", "")).date().isoformat()
                    daily_counts[date] = daily_counts.get(date, 0) + 1
                except ValueError:
                    continue
            
            analytics = {
                "period_days": days,
                "total_queries": total_queries,
                "successful_queries": successful_queries,
                "success_rate": (successful_queries / total_queries * 100) if total_queries > 0 else 0,
                "average_processing_time": round(avg_processing_time, 2),
                "most_used_models": dict(sorted(model_counts.items(), key=lambda x: x[1], reverse=True)[:5]),
                "top_query_words": dict(top_words),
                "daily_query_counts": daily_counts,
                "unique_users": len(set(e.get("user_id") for e in entries)),
                "unique_sessions": len(set(e.get("session_id") for e in entries))
            }
            
            logger.info("Generated query analytics",
                       period_days=days,
                       total_queries=total_queries,
                       user_id=user_id)
            
            return analytics
            
        except Exception as e:
            logger.error("Failed to generate analytics", error=str(e))
            return {"error": str(e)}
    
    async def export_history(self, format: str = "json",
                            user_id: Optional[str] = None,
                            start_date: Optional[str] = None,
                            end_date: Optional[str] = None) -> Optional[str]:
        """
        Export query history
        
        Args:
            format: Export format (json, csv)
            user_id: Filter by user ID
            start_date: Start date filter (ISO format)
            end_date: End date filter (ISO format)
            
        Returns:
            Export file path or None if failed
        """
        try:
            # Get filtered entries
            entries = await self.get_history(user_id=user_id, limit=10000)
            
            # Apply date filters
            if start_date or end_date:
                filtered_entries = []
                for entry in entries:
                    entry_date = entry.get("timestamp", "")
                    if start_date and entry_date < start_date:
                        continue
                    if end_date and entry_date > end_date:
                        continue
                    filtered_entries.append(entry)
                entries = filtered_entries
            
            # Generate export filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_filename = f"query_history_{timestamp}.{format}"
            export_path = self.history_dir / export_filename
            
            if format == "json":
                with open(export_path, 'w', encoding='utf-8') as f:
                    json.dump(entries, f, indent=2, ensure_ascii=False)
            
            elif format == "csv":
                import csv
                with open(export_path, 'w', newline='', encoding='utf-8') as f:
                    if entries:
                        writer = csv.DictWriter(f, fieldnames=entries[0].keys())
                        writer.writeheader()
                        for entry in entries:
                            # Flatten complex fields
                            flat_entry = entry.copy()
                            flat_entry["response"] = json.dumps(flat_entry.get("response", {}))
                            writer.writerow(flat_entry)
            
            else:
                raise ValueError(f"Unsupported export format: {format}")
            
            logger.info("Query history exported",
                       format=format,
                       entries_count=len(entries),
                       export_path=str(export_path))
            
            return str(export_path)
            
        except Exception as e:
            logger.error("Failed to export query history", error=str(e))
            return None
    
    async def _cleanup_old_entries(self):
        """Clean up old entries if exceeding max_entries"""
        try:
            if not self.history_file.exists():
                return
            
            # Count current entries
            entry_count = 0
            with open(self.history_file, 'r', encoding='utf-8') as f:
                for _ in f:
                    entry_count += 1
            
            if entry_count <= self.max_entries:
                return
            
            # Read all entries
            entries = []
            with open(self.history_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        entries.append(json.loads(line.strip()))
                    except json.JSONDecodeError:
                        continue
            
            # Sort by timestamp and keep only recent entries
            entries.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
            recent_entries = entries[:self.max_entries]
            
            # Rewrite file with recent entries only
            with open(self.history_file, 'w', encoding='utf-8') as f:
                for entry in recent_entries:
                    f.write(json.dumps(entry) + '\n')
            
            logger.info("Cleaned up old query history entries",
                       removed_count=len(entries) - len(recent_entries),
                       remaining_count=len(recent_entries))
            
        except Exception as e:
            logger.error("Failed to cleanup old entries", error=str(e))


# Global history service instance
query_history = QueryHistoryService()
