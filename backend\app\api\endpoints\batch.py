"""
Batch Processing Endpoints - Phase 5 Advanced Features
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, UploadFile, File
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
import json
import uuid
import structlog

logger = structlog.get_logger()
router = APIRouter()


class BatchQueryRequest(BaseModel):
    """Batch query request model"""
    queries: List[str] = Field(..., description="List of queries to process")
    max_chunks: int = Field(default=3, description="Maximum chunks per query")
    model_preference: Optional[str] = Field(default=None, description="Preferred LLM model")
    include_sources: bool = Field(default=True, description="Include source attribution")
    include_confidence: bool = Field(default=True, description="Include confidence scores")
    batch_name: Optional[str] = Field(default=None, description="Optional batch name")


class BatchQueryResult(BaseModel):
    """Single batch query result"""
    query: str
    success: bool
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    processing_time: float = 0.0


class BatchResponse(BaseModel):
    """Batch processing response"""
    batch_id: str = Field(..., description="Unique batch identifier")
    batch_name: Optional[str] = Field(default=None, description="Batch name")
    status: str = Field(..., description="Batch status (processing/completed/failed)")
    total_queries: int = Field(..., description="Total number of queries")
    completed_queries: int = Field(default=0, description="Number of completed queries")
    successful_queries: int = Field(default=0, description="Number of successful queries")
    failed_queries: int = Field(default=0, description="Number of failed queries")
    started_at: str = Field(..., description="Batch start timestamp")
    completed_at: Optional[str] = Field(default=None, description="Batch completion timestamp")
    total_processing_time: float = Field(default=0.0, description="Total processing time")
    results: List[BatchQueryResult] = Field(default_factory=list, description="Query results")


class ExportRequest(BaseModel):
    """Export request model"""
    format: str = Field(..., description="Export format (json, csv, xlsx)")
    include_sources: bool = Field(default=True, description="Include source information")
    include_metadata: bool = Field(default=True, description="Include query metadata")
    date_range: Optional[Dict[str, str]] = Field(default=None, description="Date range filter")
    companies: Optional[List[str]] = Field(default=None, description="Company filter")
    filing_types: Optional[List[str]] = Field(default=None, description="Filing type filter")


# In-memory batch storage (in production, use Redis or database)
batch_storage: Dict[str, BatchResponse] = {}


@router.post("/queries", response_model=BatchResponse)
async def process_batch_queries(request: BatchQueryRequest, background_tasks: BackgroundTasks):
    """
    Process multiple queries in batch
    
    Processes a list of queries asynchronously and returns batch status.
    Use the batch ID to check progress and retrieve results.
    """
    try:
        # Create batch
        batch_id = str(uuid.uuid4())
        batch_response = BatchResponse(
            batch_id=batch_id,
            batch_name=request.batch_name,
            status="processing",
            total_queries=len(request.queries),
            started_at=datetime.now().isoformat()
        )
        
        # Store batch
        batch_storage[batch_id] = batch_response
        
        # Start background processing
        background_tasks.add_task(
            _process_batch_queries,
            batch_id,
            request
        )
        
        logger.info("Batch query processing started",
                   batch_id=batch_id,
                   query_count=len(request.queries),
                   batch_name=request.batch_name)
        
        return batch_response
        
    except Exception as e:
        logger.error("Failed to start batch processing", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to start batch processing")


@router.get("/queries/{batch_id}", response_model=BatchResponse)
async def get_batch_status(batch_id: str):
    """
    Get batch processing status and results
    
    Returns the current status of a batch processing job including
    progress information and completed results.
    """
    if batch_id not in batch_storage:
        raise HTTPException(status_code=404, detail="Batch not found")
    
    batch_response = batch_storage[batch_id]
    
    logger.info("Batch status retrieved",
               batch_id=batch_id,
               status=batch_response.status,
               completed=batch_response.completed_queries)
    
    return batch_response


@router.get("/queries")
async def list_batches(limit: int = 20, offset: int = 0):
    """
    List recent batch processing jobs
    
    Returns a list of recent batch jobs with their status and summary information.
    """
    try:
        # Get all batches sorted by start time
        all_batches = list(batch_storage.values())
        all_batches.sort(key=lambda x: x.started_at, reverse=True)
        
        # Apply pagination
        paginated_batches = all_batches[offset:offset + limit]
        
        # Create summary
        summary = {
            "total_batches": len(all_batches),
            "processing_batches": sum(1 for b in all_batches if b.status == "processing"),
            "completed_batches": sum(1 for b in all_batches if b.status == "completed"),
            "failed_batches": sum(1 for b in all_batches if b.status == "failed"),
            "batches": [
                {
                    "batch_id": batch.batch_id,
                    "batch_name": batch.batch_name,
                    "status": batch.status,
                    "total_queries": batch.total_queries,
                    "completed_queries": batch.completed_queries,
                    "successful_queries": batch.successful_queries,
                    "started_at": batch.started_at,
                    "completed_at": batch.completed_at
                }
                for batch in paginated_batches
            ]
        }
        
        logger.info("Batch list retrieved", total_batches=len(all_batches))
        return summary
        
    except Exception as e:
        logger.error("Failed to list batches", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to list batches")


@router.delete("/queries/{batch_id}")
async def delete_batch(batch_id: str):
    """
    Delete a batch processing job
    
    Removes a batch job from storage. Cannot delete processing batches.
    """
    if batch_id not in batch_storage:
        raise HTTPException(status_code=404, detail="Batch not found")
    
    batch = batch_storage[batch_id]
    
    if batch.status == "processing":
        raise HTTPException(status_code=400, detail="Cannot delete processing batch")
    
    del batch_storage[batch_id]
    
    logger.info("Batch deleted", batch_id=batch_id)
    return {"message": "Batch deleted successfully"}


@router.post("/export")
async def export_data(request: ExportRequest, background_tasks: BackgroundTasks):
    """
    Export query results and data
    
    Creates an export file with query results, company data, or filing information
    based on the specified filters and format.
    """
    try:
        # Generate export ID
        export_id = str(uuid.uuid4())
        
        # Start background export
        background_tasks.add_task(
            _process_export,
            export_id,
            request
        )
        
        logger.info("Export started",
                   export_id=export_id,
                   format=request.format)
        
        return {
            "export_id": export_id,
            "status": "processing",
            "format": request.format,
            "started_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to start export", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to start export")


@router.post("/upload")
async def upload_batch_queries(file: UploadFile = File(...)):
    """
    Upload batch queries from file
    
    Accepts CSV, JSON, or TXT files containing queries for batch processing.
    Returns a batch ID for tracking the processing status.
    """
    try:
        # Read file content
        content = await file.read()
        
        # Parse queries based on file type
        queries = []
        
        if file.filename.endswith('.json'):
            data = json.loads(content.decode('utf-8'))
            if isinstance(data, list):
                queries = [str(q) for q in data]
            elif isinstance(data, dict) and 'queries' in data:
                queries = [str(q) for q in data['queries']]
            else:
                raise ValueError("Invalid JSON format")
                
        elif file.filename.endswith('.csv'):
            import csv
            import io
            csv_reader = csv.reader(io.StringIO(content.decode('utf-8')))
            for row in csv_reader:
                if row:  # Skip empty rows
                    queries.append(row[0])  # Use first column
                    
        elif file.filename.endswith('.txt'):
            lines = content.decode('utf-8').split('\n')
            queries = [line.strip() for line in lines if line.strip()]
            
        else:
            raise ValueError("Unsupported file format")
        
        if not queries:
            raise ValueError("No queries found in file")
        
        # Create batch request
        batch_request = BatchQueryRequest(
            queries=queries,
            batch_name=f"Upload: {file.filename}"
        )
        
        # Process batch
        batch_id = str(uuid.uuid4())
        batch_response = BatchResponse(
            batch_id=batch_id,
            batch_name=batch_request.batch_name,
            status="processing",
            total_queries=len(queries),
            started_at=datetime.now().isoformat()
        )
        
        batch_storage[batch_id] = batch_response
        
        # Start processing in background
        from fastapi import BackgroundTasks
        background_tasks = BackgroundTasks()
        background_tasks.add_task(_process_batch_queries, batch_id, batch_request)
        
        logger.info("Batch queries uploaded",
                   filename=file.filename,
                   query_count=len(queries),
                   batch_id=batch_id)
        
        return {
            "batch_id": batch_id,
            "filename": file.filename,
            "query_count": len(queries),
            "status": "processing"
        }
        
    except Exception as e:
        logger.error("Failed to upload batch queries", error=str(e))
        raise HTTPException(status_code=400, detail=f"Failed to process file: {str(e)}")


async def _process_batch_queries(batch_id: str, request: BatchQueryRequest):
    """Background task to process batch queries"""
    try:
        from app.services.query_engine import query_engine
        
        batch = batch_storage[batch_id]
        results = []
        
        for i, query in enumerate(request.queries):
            try:
                start_time = datetime.now()
                
                # Process query
                result = await query_engine.process_query(
                    query=query,
                    max_chunks=request.max_chunks,
                    model_preference=request.model_preference
                )
                
                processing_time = (datetime.now() - start_time).total_seconds()
                
                # Create result
                query_result = BatchQueryResult(
                    query=query,
                    success=result.get("success", False),
                    result=result if request.include_sources else {
                        k: v for k, v in result.items() 
                        if k not in ["sources", "context_sources"]
                    },
                    processing_time=processing_time
                )
                
                results.append(query_result)
                
                # Update batch status
                batch.completed_queries = i + 1
                if result.get("success", False):
                    batch.successful_queries += 1
                else:
                    batch.failed_queries += 1
                
            except Exception as e:
                # Handle query error
                query_result = BatchQueryResult(
                    query=query,
                    success=False,
                    error=str(e),
                    processing_time=0.0
                )
                results.append(query_result)
                batch.completed_queries = i + 1
                batch.failed_queries += 1
        
        # Update final batch status
        batch.status = "completed"
        batch.completed_at = datetime.now().isoformat()
        batch.results = results
        batch.total_processing_time = sum(r.processing_time for r in results)
        
        logger.info("Batch processing completed",
                   batch_id=batch_id,
                   successful=batch.successful_queries,
                   failed=batch.failed_queries)
        
    except Exception as e:
        # Update batch with error status
        batch = batch_storage.get(batch_id)
        if batch:
            batch.status = "failed"
            batch.completed_at = datetime.now().isoformat()
        
        logger.error("Batch processing failed", batch_id=batch_id, error=str(e))


async def _process_export(export_id: str, request: ExportRequest):
    """Background task to process export"""
    try:
        # This would implement the actual export logic
        # For now, just log the export request
        logger.info("Export processing started",
                   export_id=export_id,
                   format=request.format)
        
        # Simulate export processing
        import asyncio
        await asyncio.sleep(2)
        
        logger.info("Export processing completed", export_id=export_id)
        
    except Exception as e:
        logger.error("Export processing failed", export_id=export_id, error=str(e))
