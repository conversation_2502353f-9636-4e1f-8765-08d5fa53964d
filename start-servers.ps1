# PowerShell Script to Start SEC Filing QA Agent Servers
# Starts both FastAPI backend and React frontend

param(
    [switch]$NoWait,
    [switch]$Help
)

if ($Help) {
    Write-Host @"
SEC Filing QA Agent - Server Startup Script

Usage: .\start-servers.ps1 [options]

Options:
  -NoWait    Don't wait for user input at the end
  -Help      Show this help message

This script will:
1. Check prerequisites (Python, Node.js)
2. Install dependencies if needed
3. Start both backend and frontend servers
4. Provide testing instructions

"@
    exit 0
}

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  SEC Filing QA Agent - Server Startup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to print colored output
function Write-Success {
    param($Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param($Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param($Message)
    Write-Host "ℹ️ $Message" -ForegroundColor Blue
}

function Write-Warning {
    param($Message)
    Write-Host "⚠️ $Message" -ForegroundColor Yellow
}

# Check if directories exist
if (-not (Test-Path "backend")) {
    Write-Error "Backend directory not found!"
    Write-Host "Please run this script from the project root directory."
    if (-not $NoWait) { Read-Host "Press Enter to exit" }
    exit 1
}

if (-not (Test-Path "frontend")) {
    Write-Error "Frontend directory not found!"
    Write-Host "Please run this script from the project root directory."
    if (-not $NoWait) { Read-Host "Press Enter to exit" }
    exit 1
}

Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow

# Check Python
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Python found: $pythonVersion"
    } else {
        throw "Python not found"
    }
} catch {
    Write-Error "Python not found! Please install Python 3.8+"
    if (-not $NoWait) { Read-Host "Press Enter to exit" }
    exit 1
}

# Check Node.js/npm
try {
    $npmVersion = npm --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Success "npm found: v$npmVersion"
    } else {
        throw "npm not found"
    }
} catch {
    Write-Error "npm not found! Please install Node.js"
    if (-not $NoWait) { Read-Host "Press Enter to exit" }
    exit 1
}

Write-Success "Prerequisites check passed"
Write-Host ""

# Install backend dependencies if needed
Write-Host "📦 Checking backend dependencies..." -ForegroundColor Yellow
Push-Location backend

if (-not (Test-Path "venv")) {
    Write-Host "Creating Python virtual environment..."
    python -m venv venv
}

Write-Host "Activating virtual environment..."
& "venv\Scripts\Activate.ps1"

Write-Host "Installing/updating backend dependencies..."
pip install -r requirements.txt | Out-Null

Pop-Location

# Install frontend dependencies if needed
Write-Host "📦 Checking frontend dependencies..." -ForegroundColor Yellow
Push-Location frontend
if (-not (Test-Path "node_modules")) {
    Write-Host "Installing frontend dependencies..."
    npm install
}
Pop-Location

Write-Host ""
Write-Host "🚀 Starting servers..." -ForegroundColor Yellow
Write-Host ""

# Function to start backend
function Start-Backend {
    Write-Host "🔧 Starting Backend Server (FastAPI)..." -ForegroundColor Blue
    
    $backendJob = Start-Job -ScriptBlock {
        Set-Location $using:PWD
        Set-Location backend
        & "venv\Scripts\Activate.ps1"
        python -m uvicorn app.main:app --reload --host 127.0.0.1 --port 8000
    }
    
    return $backendJob
}

# Function to start frontend
function Start-Frontend {
    Write-Host "🌐 Starting Frontend Server (React)..." -ForegroundColor Blue
    
    $frontendJob = Start-Job -ScriptBlock {
        Set-Location $using:PWD
        Set-Location frontend
        npm run dev
    }
    
    return $frontendJob
}

# Start servers
$backendJob = Start-Backend
Start-Sleep -Seconds 5
$frontendJob = Start-Frontend

# Wait for servers to start
Write-Host "⏳ Waiting for servers to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check server status
Write-Host ""
Write-Host "🔍 Checking server status..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:8000/api/v1/health/" -TimeoutSec 5 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Success "Backend Server: RUNNING (http://127.0.0.1:8000)"
    }
} catch {
    Write-Warning "Backend Server: Starting... (may take a moment)"
}

try {
    $response = Invoke-WebRequest -Uri "http://localhost:5173" -TimeoutSec 5 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Success "Frontend Server: RUNNING (http://localhost:5173)"
    }
} catch {
    Write-Warning "Frontend Server: Starting... (may take a moment)"
}

Write-Host ""
Write-Host "🎉 SERVERS STARTED!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🌐 Frontend: http://localhost:5173" -ForegroundColor White
Write-Host "🔧 Backend:  http://127.0.0.1:8000" -ForegroundColor White
Write-Host "📚 API Docs: http://127.0.0.1:8000/docs" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "💡 TESTING INSTRUCTIONS:" -ForegroundColor Yellow
Write-Host "1. Open http://localhost:5173 in your browser"
Write-Host "2. Try asking: 'What are Apple's main revenue sources?'"
Write-Host "3. Test advanced features: history, bookmarks, export"
Write-Host ""
Write-Host "🧪 RUN QUICK TEST:" -ForegroundColor Yellow
Write-Host "   python quick_test.py"
Write-Host ""
Write-Host "🧪 RUN FULL INTEGRATION TEST:" -ForegroundColor Yellow
Write-Host "   python test_integration_complete.py"
Write-Host ""
Write-Host "📋 VIEW SERVER LOGS:" -ForegroundColor Yellow
Write-Host "   Backend:  Receive-Job $($backendJob.Id) -Keep"
Write-Host "   Frontend: Receive-Job $($frontendJob.Id) -Keep"
Write-Host ""

# Function to cleanup jobs
function Stop-Servers {
    Write-Host ""
    Write-Info "Stopping servers..."
    
    if ($backendJob) {
        Stop-Job $backendJob -ErrorAction SilentlyContinue
        Remove-Job $backendJob -ErrorAction SilentlyContinue
    }
    
    if ($frontendJob) {
        Stop-Job $frontendJob -ErrorAction SilentlyContinue
        Remove-Job $frontendJob -ErrorAction SilentlyContinue
    }
    
    Write-Host "👋 Servers stopped. Goodbye!" -ForegroundColor Green
}

# Set up Ctrl+C handler
$null = Register-EngineEvent PowerShell.Exiting -Action {
    Stop-Servers
}

if (-not $NoWait) {
    Write-Host "⚠️  Press Ctrl+C or close this window to stop both servers" -ForegroundColor Yellow
    Write-Host ""
    
    try {
        # Keep script running
        while ($true) {
            Start-Sleep -Seconds 1
            
            # Check if jobs are still running
            if ($backendJob.State -eq "Completed" -or $backendJob.State -eq "Failed") {
                Write-Error "Backend server stopped unexpectedly"
                break
            }
            
            if ($frontendJob.State -eq "Completed" -or $frontendJob.State -eq "Failed") {
                Write-Error "Frontend server stopped unexpectedly"
                break
            }
        }
    } catch {
        # Handle Ctrl+C
    } finally {
        Stop-Servers
    }
} else {
    Write-Host "Servers started in background. Use Stop-Job to stop them." -ForegroundColor Yellow
}
