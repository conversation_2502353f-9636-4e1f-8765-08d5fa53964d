/**
 * Enhanced HomePage Component
 * Phase 6: Frontend Development - Task 6.1 Complete
 */

import React, { useState, useCallback } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Snackbar,
  Grid,
  Fab,
  Button,
  useTheme,
  useMediaQ<PERSON>y,
  <PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IconButton,
} from '@mui/material';
import {
  History as HistoryIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
// Import our new components
import QueryInterface from '../components/QueryInterface';
import QueryResults from '../components/QueryResults';
import FiltersPanel from '../components/FiltersPanel';
import QueryHistory from '../components/QueryHistory';
import ExportDialog from '../components/ExportDialog';
import AdvancedFilters from '../components/AdvancedFilters';
import {
  QueryLoading,
  ErrorDisplay,
  NoResults,
  OfflineIndicator
} from '../components/LoadingStates';

// Import context and hooks
import { useQueryState, useBookmarks, useQueryHistory } from '../contexts/AppContext';
import {
  useCompanies,
  useSupportedCompanies,
  useSubmitQuery,
  useApiConnection
} from '../hooks/useApi';
import { QueryRequest, QueryResponse, QueryFilters } from '../types/api';

const HomePage: React.FC = () => {
  // Theme and responsive design
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  // Context and state management
  const { currentQuery, filters, setCurrentQuery, addToHistory, setFilters } = useQueryState();
  const { addBookmark, removeBookmark, isBookmarked } = useBookmarks();
  const { getRecentQueries } = useQueryHistory();

  // Local state
  const [queryResult, setQueryResult] = useState<QueryResponse | null>(null);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');
  const [showHistory, setShowHistory] = useState(false);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [exportType, setExportType] = useState<'query' | 'history'>('query');

  // API connection status
  const { isOffline } = useApiConnection();

  // Get recent queries
  const recentQueries = getRecentQueries(10);

  // Load companies data using custom hooks
  const {
    data: companiesData,
    isLoading: companiesLoading,
    error: companiesError
  } = useCompanies({ has_qa_support: true, page_size: 100 });

  const companies = companiesData?.companies || [];

  // Load supported companies for query interface
  const {
    data: supportedCompanies = [],
    isLoading: supportedLoading
  } = useSupportedCompanies();

  // Query mutation using custom hook
  const queryMutation = useSubmitQuery({
    onSuccess: (data, variables) => {
      setQueryResult(data);
      setCurrentQuery(variables.question);

      // Add to history using context
      addToHistory(variables.question);

      // Show success message
      if (data.success) {
        setSnackbarMessage('Query processed successfully!');
      }
    },
    onError: (error: any) => {
      console.error('Query failed:', error);
      setQueryResult({
        success: false,
        answer: '',
        sources: [],
        confidence: '',
        processing_time: 0,
        model_used: '',
        context_chunks: 0,
        error: error.detail || error.message || 'Query failed',
        suggestions: [
          'Try rephrasing your question',
          'Be more specific about the company or time period',
          'Check if the company is supported',
          'Ensure your question is about SEC filings'
        ]
      });
    },
  });

  // Handle query submission
  const handleQuerySubmit = useCallback((request: QueryRequest) => {
    // Apply filters to the request if any
    const enhancedRequest = {
      ...request,
      // TODO: Apply filters to query context
    };

    queryMutation.mutate(enhancedRequest);
  }, [queryMutation]);

  // Handle bookmark
  const handleBookmark = useCallback((query: string, _answer: string) => {
    if (isBookmarked(query)) {
      removeBookmark(query);
      setSnackbarMessage('Bookmark removed!');
    } else {
      addBookmark(query);
      setSnackbarMessage('Query bookmarked!');
    }
  }, [addBookmark, removeBookmark, isBookmarked]);

  // Handle filter changes
  const handleFiltersChange = useCallback((newFilters: QueryFilters) => {
    setFilters(newFilters);
    // TODO: Re-run query with new filters if there's an active result
  }, []);

  // Clear error
  const handleClearError = useCallback(() => {
    if (queryResult && !queryResult.success) {
      setQueryResult(null);
    }
  }, [queryResult]);

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box textAlign="center" mb={4}>
        <Typography variant="h2" component="h1" gutterBottom>
          SEC Filing QA Agent
        </Typography>
        <Typography variant="h5" color="text.secondary" gutterBottom>
          Ask intelligent questions about SEC filings and get AI-powered answers
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Powered by advanced AI with source attribution and real-time analysis
        </Typography>
      </Box>

      {/* Offline Indicator */}
      {isOffline && <OfflineIndicator />}

      {/* Companies Loading Error */}
      {companiesError && (
        <ErrorDisplay
          error="Failed to load company data. Some features may be limited."
          type="warning"
          showRetry={false}
        />
      )}

      {/* Filters Panel */}
      <FiltersPanel
        companies={companies}
        filters={filters}
        onFiltersChange={handleFiltersChange}
        isLoading={queryMutation.isPending}
        compact={true}
      />

      {/* Query Interface */}
      <QueryInterface
        onSubmit={handleQuerySubmit}
        isLoading={queryMutation.isPending}
        error={queryResult && !queryResult.success ? queryResult.error : undefined}
        supportedCompanies={supportedCompanies}
        recentQueries={recentQueries}
        onClearError={handleClearError}
      />

      {/* Loading State */}
      {queryMutation.isPending && (
        <QueryLoading message="Analyzing your query and searching SEC filings..." />
      )}

      {/* Query Results */}
      {queryResult && queryResult.success && (
        <QueryResults
          result={queryResult}
          onBookmark={handleBookmark}
          isBookmarked={isBookmarked(currentQuery)}
        />
      )}

      {/* No Results */}
      {queryResult && queryResult.success && !queryResult.answer && (
        <NoResults
          query={currentQuery}
          suggestions={[
            'Try asking about specific financial metrics',
            'Include company ticker symbols (e.g., AAPL, MSFT)',
            'Ask about recent quarterly or annual reports',
            'Be more specific about the time period'
          ]}
        />
      )}

      {/* Welcome Message (when no query has been made) */}
      {!queryResult && !queryMutation.isPending && (
        <Paper elevation={2} sx={{ p: 4, textAlign: 'center', bgcolor: 'primary.light' }}>
          <Typography variant="h6" gutterBottom>
            Welcome to SEC Filing QA Agent
          </Typography>
          <Typography variant="body1" sx={{ mb: 3 }}>
            Ask natural language questions about SEC filings and get intelligent,
            source-attributed answers powered by advanced AI.
          </Typography>

          <Box display="flex" justifyContent="center" flexWrap="wrap" gap={2}>
            <Paper elevation={1} sx={{ p: 2, maxWidth: 200 }}>
              <Typography variant="subtitle2" gutterBottom>
                📊 Financial Analysis
              </Typography>
              <Typography variant="caption">
                Revenue, profits, growth metrics, and financial performance
              </Typography>
            </Paper>

            <Paper elevation={1} sx={{ p: 2, maxWidth: 200 }}>
              <Typography variant="subtitle2" gutterBottom>
                ⚠️ Risk Assessment
              </Typography>
              <Typography variant="caption">
                Risk factors, challenges, and business uncertainties
              </Typography>
            </Paper>

            <Paper elevation={1} sx={{ p: 2, maxWidth: 200 }}>
              <Typography variant="subtitle2" gutterBottom>
                🔍 Company Comparison
              </Typography>
              <Typography variant="caption">
                Compare metrics and strategies across companies
              </Typography>
            </Paper>
          </Box>
        </Paper>
      )}

      {/* Mobile Floating Action Buttons */}
      {isMobile && (
        <Box
          position="fixed"
          bottom={16}
          right={16}
          display="flex"
          flexDirection="column"
          gap={1}
          zIndex={1000}
        >
          <Fab
            color="primary"
            size="small"
            onClick={() => setShowHistory(true)}
            sx={{ mb: 1 }}
          >
            <HistoryIcon />
          </Fab>

          <Fab
            color="secondary"
            size="small"
            onClick={() => setShowAdvancedFilters(true)}
            sx={{ mb: 1 }}
          >
            <FilterIcon />
          </Fab>

          {queryResult && queryResult.success && (
            <Fab
              color="default"
              size="small"
              onClick={() => {
                setExportType('query');
                setShowExportDialog(true);
              }}
            >
              <DownloadIcon />
            </Fab>
          )}
        </Box>
      )}

      {/* Desktop Sidebar Components */}
      {!isMobile && (
        <Grid container spacing={3} sx={{ mt: 2 }}>
          <Grid item xs={12} md={8}>
            {/* Main content already rendered above */}
          </Grid>

          <Grid item xs={12} md={4}>
            {/* Query History */}
            <QueryHistory
              onQuerySelect={(query) => {
                setCurrentQuery(query);
                // Optionally auto-submit the query
              }}
              maxItems={10}
              compact={true}
            />

            {/* Export Options */}
            {queryResult && queryResult.success && (
              <Paper elevation={2} sx={{ p: 2, mb: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Export Options
                </Typography>
                <Box display="flex" gap={1}>
                  <Button
                    size="small"
                    startIcon={<DownloadIcon />}
                    onClick={() => {
                      setExportType('query');
                      setShowExportDialog(true);
                    }}
                  >
                    Export Result
                  </Button>
                  <Button
                    size="small"
                    startIcon={<HistoryIcon />}
                    onClick={() => {
                      setExportType('history');
                      setShowExportDialog(true);
                    }}
                  >
                    Export History
                  </Button>
                </Box>
              </Paper>
            )}
          </Grid>
        </Grid>
      )}

      {/* Mobile Drawers */}
      {showHistory && (
        <Drawer
          anchor="right"
          open={showHistory}
          onClose={() => setShowHistory(false)}
          PaperProps={{
            sx: { width: isSmallScreen ? '100%' : 400 }
          }}
        >
          <AppBar position="static" elevation={0}>
            <Toolbar>
              <HistoryIcon sx={{ mr: 2 }} />
              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                Query History
              </Typography>
              <IconButton color="inherit" onClick={() => setShowHistory(false)}>
                <CloseIcon />
              </IconButton>
            </Toolbar>
          </AppBar>

          <Box sx={{ p: 2 }}>
            <QueryHistory
              onQuerySelect={(query) => {
                setCurrentQuery(query);
                setShowHistory(false);
              }}
              maxItems={50}
              compact={false}
            />
          </Box>
        </Drawer>
      )}

      {/* Advanced Filters Drawer */}
      {showAdvancedFilters && (
        <AdvancedFilters
          filters={filters}
          onFiltersChange={setFilters}
          companies={companies}
          mobile={true}
          onClose={() => setShowAdvancedFilters(false)}
        />
      )}

      {/* Export Dialog */}
      <ExportDialog
        open={showExportDialog}
        onClose={() => setShowExportDialog(false)}
        queryResult={queryResult}
        exportType={exportType}
      />

      {/* Success Snackbar */}
      <Snackbar
        open={!!snackbarMessage}
        autoHideDuration={3000}
        onClose={() => setSnackbarMessage('')}
        message={snackbarMessage}
      />
    </Container>
  );
};

export default HomePage;
