/**
 * Enhanced HomePage Component
 * Phase 6: Frontend Development - Task 6.1 Complete
 */

import React, { useState, useCallback } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Snackbar,
} from '@mui/material';
// Import our new components
import QueryInterface from '../components/QueryInterface';
import QueryResults from '../components/QueryResults';
import FiltersPanel from '../components/FiltersPanel';
import {
  QueryLoading,
  ErrorDisplay,
  NoResults,
  OfflineIndicator
} from '../components/LoadingStates';

// Import context and hooks
import { useQueryState, useBookmarks, useQueryHistory } from '../contexts/AppContext';
import {
  useCompanies,
  useSupportedCompanies,
  useSubmitQuery,
  useApiConnection
} from '../hooks/useApi';
import { QueryRequest, QueryResponse, QueryFilters } from '../types/api';

const HomePage: React.FC = () => {
  // Context and state management
  const { currentQuery, filters, setCurrentQuery, addToHistory, setFilters } = useQueryState();
  const { addBookmark, removeBookmark, isBookmarked } = useBookmarks();
  const { getRecentQueries } = useQueryHistory();

  // Local state
  const [queryResult, setQueryResult] = useState<QueryResponse | null>(null);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');

  // API connection status
  const { isConnected, isOffline } = useApiConnection();

  // Get recent queries
  const recentQueries = getRecentQueries(10);

  // Load companies data using custom hooks
  const {
    data: companiesData,
    isLoading: companiesLoading,
    error: companiesError
  } = useCompanies({ has_qa_support: true, page_size: 100 });

  const companies = companiesData?.companies || [];

  // Load supported companies for query interface
  const {
    data: supportedCompanies = [],
    isLoading: supportedLoading
  } = useSupportedCompanies();

  // Query mutation using custom hook
  const queryMutation = useSubmitQuery({
    onSuccess: (data, variables) => {
      setQueryResult(data);
      setCurrentQuery(variables.question);

      // Add to history using context
      addToHistory(variables.question);

      // Show success message
      if (data.success) {
        setSnackbarMessage('Query processed successfully!');
      }
    },
    onError: (error: any) => {
      console.error('Query failed:', error);
      setQueryResult({
        success: false,
        answer: '',
        sources: [],
        confidence: '',
        processing_time: 0,
        model_used: '',
        context_chunks: 0,
        error: error.detail || error.message || 'Query failed',
        suggestions: [
          'Try rephrasing your question',
          'Be more specific about the company or time period',
          'Check if the company is supported',
          'Ensure your question is about SEC filings'
        ]
      });
    },
  });

  // Handle query submission
  const handleQuerySubmit = useCallback((request: QueryRequest) => {
    // Apply filters to the request if any
    const enhancedRequest = {
      ...request,
      // TODO: Apply filters to query context
    };

    queryMutation.mutate(enhancedRequest);
  }, [queryMutation]);

  // Handle bookmark
  const handleBookmark = useCallback((query: string, answer: string) => {
    if (isBookmarked(query)) {
      removeBookmark(query);
      setSnackbarMessage('Bookmark removed!');
    } else {
      addBookmark(query);
      setSnackbarMessage('Query bookmarked!');
    }
  }, [addBookmark, removeBookmark, isBookmarked]);

  // Handle filter changes
  const handleFiltersChange = useCallback((newFilters: QueryFilters) => {
    setFilters(newFilters);
    // TODO: Re-run query with new filters if there's an active result
  }, []);

  // Clear error
  const handleClearError = useCallback(() => {
    if (queryResult && !queryResult.success) {
      setQueryResult(null);
    }
  }, [queryResult]);

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box textAlign="center" mb={4}>
        <Typography variant="h2" component="h1" gutterBottom>
          SEC Filing QA Agent
        </Typography>
        <Typography variant="h5" color="text.secondary" gutterBottom>
          Ask intelligent questions about SEC filings and get AI-powered answers
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Powered by advanced AI with source attribution and real-time analysis
        </Typography>
      </Box>

      {/* Offline Indicator */}
      {isOffline && <OfflineIndicator />}

      {/* Companies Loading Error */}
      {companiesError && (
        <ErrorDisplay
          error="Failed to load company data. Some features may be limited."
          type="warning"
          showRetry={false}
        />
      )}

      {/* Filters Panel */}
      <FiltersPanel
        companies={companies}
        filters={filters}
        onFiltersChange={handleFiltersChange}
        isLoading={queryMutation.isPending}
        compact={true}
      />

      {/* Query Interface */}
      <QueryInterface
        onSubmit={handleQuerySubmit}
        isLoading={queryMutation.isPending}
        error={queryResult && !queryResult.success ? queryResult.error : undefined}
        supportedCompanies={supportedCompanies}
        recentQueries={recentQueries}
        onClearError={handleClearError}
      />

      {/* Loading State */}
      {queryMutation.isPending && (
        <QueryLoading message="Analyzing your query and searching SEC filings..." />
      )}

      {/* Query Results */}
      {queryResult && queryResult.success && (
        <QueryResults
          result={queryResult}
          onBookmark={handleBookmark}
          isBookmarked={isBookmarked(currentQuery)}
        />
      )}

      {/* No Results */}
      {queryResult && queryResult.success && !queryResult.answer && (
        <NoResults
          query={currentQuery}
          suggestions={[
            'Try asking about specific financial metrics',
            'Include company ticker symbols (e.g., AAPL, MSFT)',
            'Ask about recent quarterly or annual reports',
            'Be more specific about the time period'
          ]}
        />
      )}

      {/* Welcome Message (when no query has been made) */}
      {!queryResult && !queryMutation.isPending && (
        <Paper elevation={2} sx={{ p: 4, textAlign: 'center', bgcolor: 'primary.light' }}>
          <Typography variant="h6" gutterBottom>
            Welcome to SEC Filing QA Agent
          </Typography>
          <Typography variant="body1" sx={{ mb: 3 }}>
            Ask natural language questions about SEC filings and get intelligent,
            source-attributed answers powered by advanced AI.
          </Typography>

          <Box display="flex" justifyContent="center" flexWrap="wrap" gap={2}>
            <Paper elevation={1} sx={{ p: 2, maxWidth: 200 }}>
              <Typography variant="subtitle2" gutterBottom>
                📊 Financial Analysis
              </Typography>
              <Typography variant="caption">
                Revenue, profits, growth metrics, and financial performance
              </Typography>
            </Paper>

            <Paper elevation={1} sx={{ p: 2, maxWidth: 200 }}>
              <Typography variant="subtitle2" gutterBottom>
                ⚠️ Risk Assessment
              </Typography>
              <Typography variant="caption">
                Risk factors, challenges, and business uncertainties
              </Typography>
            </Paper>

            <Paper elevation={1} sx={{ p: 2, maxWidth: 200 }}>
              <Typography variant="subtitle2" gutterBottom>
                🔍 Company Comparison
              </Typography>
              <Typography variant="caption">
                Compare metrics and strategies across companies
              </Typography>
            </Paper>
          </Box>
        </Paper>
      )}

      {/* Success Snackbar */}
      <Snackbar
        open={!!snackbarMessage}
        autoHideDuration={3000}
        onClose={() => setSnackbarMessage('')}
        message={snackbarMessage}
      />
    </Container>
  );
};

export default HomePage;
