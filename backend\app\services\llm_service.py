"""
LLM Service
Handles language model integration with OpenRouter API using free tier models
"""

import asyncio
import json
from typing import List, Dict, Any, Optional
import structlog
from datetime import datetime
import httpx

from app.core.config import settings

logger = structlog.get_logger()


class OpenRouterLLMService:
    """
    LLM service using OpenRouter API with free tier models
    """
    
    def __init__(self):
        self.base_url = settings.OPENROUTER_BASE_URL
        self.api_key = settings.OPENROUTER_API_KEY
        
        # Free tier models available on OpenRouter
        self.models = {
            "gemma": {
                "name": "google/gemma-2-9b-it:free",
                "description": "Google Gemma 2 9B Instruct - Fast and efficient",
                "max_tokens": 8192,
                "context_window": 8192
            },
            "deepseek": {
                "name": "deepseek/deepseek-r1-distill-llama-70b:free", 
                "description": "DeepSeek R1 Distill Llama 70B - Reasoning focused",
                "max_tokens": 8192,
                "context_window": 32768
            }
        }
        
        # Default model selection
        self.primary_model = "gemma"
        self.fallback_model = "deepseek"
        
        # Request tracking
        self.request_count = 0
        self.error_count = 0
        self.last_error: Optional[str] = None
        
        # Initialize HTTP client
        headers = {
            "Content-Type": "application/json",
            "HTTP-Referer": "https://sec-filings-qa.local",
            "X-Title": "SEC Filings QA Agent"
        }

        # Only add Authorization header if API key is provided
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"

        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(60.0),
            headers=headers
        )
        
        logger.info("OpenRouter LLM service initialized", 
                   primary_model=self.models[self.primary_model]["name"],
                   fallback_model=self.models[self.fallback_model]["name"])
    
    async def generate_answer(self, query: str, context_chunks: List[Dict[str, Any]], 
                            model_preference: str = None) -> Dict[str, Any]:
        """
        Generate an answer using the LLM with retrieved context
        
        Args:
            query: User's question
            context_chunks: Retrieved document chunks with metadata
            model_preference: Preferred model ("gemma" or "deepseek")
            
        Returns:
            Dictionary with answer and metadata
        """
        model_key = model_preference or self.primary_model
        
        logger.info("Generating LLM answer", 
                   query_length=len(query),
                   context_chunks=len(context_chunks),
                   model=model_key)
        
        try:
            # Try primary model first
            result = await self._generate_with_model(query, context_chunks, model_key)
            return result
            
        except Exception as e:
            logger.error("Primary model failed", model=model_key, error=str(e))
            
            # Try fallback model if primary fails
            if model_key != self.fallback_model:
                logger.info("Trying fallback model", fallback_model=self.fallback_model)
                try:
                    result = await self._generate_with_model(query, context_chunks, self.fallback_model)
                    result["used_fallback"] = True
                    result["fallback_reason"] = str(e)
                    return result
                    
                except Exception as fallback_error:
                    logger.error("Fallback model also failed", 
                               fallback_model=self.fallback_model,
                               error=str(fallback_error))
                    raise Exception(f"Both models failed. Primary: {str(e)}, Fallback: {str(fallback_error)}")
            else:
                raise
    
    async def _generate_with_model(self, query: str, context_chunks: List[Dict[str, Any]],
                                 model_key: str) -> Dict[str, Any]:
        """Generate answer with specific model"""
        if not self.api_key:
            raise Exception("OpenRouter API key not configured. Please set OPENROUTER_API_KEY environment variable.")

        model_info = self.models[model_key]
        
        # Prepare context from chunks
        context_text = self._prepare_context(context_chunks)
        
        # Create prompt
        prompt = self._create_prompt(query, context_text, context_chunks)
        
        # Prepare request
        request_data = {
            "model": model_info["name"],
            "messages": [
                {
                    "role": "system",
                    "content": "You are a financial analyst AI assistant specializing in SEC filings analysis. Provide accurate, detailed answers based on the provided context. Always cite specific sources and be transparent about limitations."
                },
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            "max_tokens": min(2000, model_info["max_tokens"]),
            "temperature": 0.1,
            "top_p": 0.9,
            "stream": False
        }
        
        # Make API request
        start_time = datetime.now()
        
        try:
            response = await self.client.post(
                f"{self.base_url}/chat/completions",
                json=request_data
            )
            
            response.raise_for_status()
            response_data = response.json()
            
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds()
            
            # Extract answer
            answer = response_data["choices"][0]["message"]["content"]
            
            # Track usage
            self.request_count += 1
            usage = response_data.get("usage", {})
            
            result = {
                "answer": answer,
                "model_used": model_info["name"],
                "model_key": model_key,
                "response_time_seconds": response_time,
                "context_chunks_used": len(context_chunks),
                "sources": self._extract_sources(context_chunks),
                "usage": usage,
                "timestamp": datetime.now().isoformat(),
                "used_fallback": False
            }
            
            logger.info("LLM answer generated successfully",
                       model=model_key,
                       response_time=response_time,
                       answer_length=len(answer),
                       tokens_used=usage.get("total_tokens", 0))
            
            return result
            
        except httpx.HTTPStatusError as e:
            self.error_count += 1
            self.last_error = f"HTTP {e.response.status_code}: {e.response.text}"
            logger.error("OpenRouter API error", 
                        status_code=e.response.status_code,
                        error=e.response.text)
            raise Exception(f"OpenRouter API error: {self.last_error}")
            
        except Exception as e:
            self.error_count += 1
            self.last_error = str(e)
            logger.error("LLM generation failed", model=model_key, error=str(e))
            raise
    
    def _prepare_context(self, context_chunks: List[Dict[str, Any]]) -> str:
        """Prepare context text from retrieved chunks"""
        context_parts = []
        
        for i, chunk in enumerate(context_chunks, 1):
            metadata = chunk.get("metadata", {})
            content = metadata.get("content_preview", "No content available")
            
            # Create source reference
            ticker = metadata.get("ticker", "Unknown")
            filing_type = metadata.get("filing_type", "Unknown")
            section = metadata.get("section_name", "Unknown")
            
            context_part = f"""
Source {i} [{ticker} {filing_type} - {section}]:
{content}
"""
            context_parts.append(context_part.strip())
        
        return "\n\n".join(context_parts)
    
    def _create_prompt(self, query: str, context_text: str, context_chunks: List[Dict[str, Any]]) -> str:
        """Create the prompt for the LLM"""
        
        prompt = f"""Based on the following SEC filing information, please answer the user's question accurately and comprehensively.

CONTEXT FROM SEC FILINGS:
{context_text}

USER QUESTION:
{query}

INSTRUCTIONS:
1. Answer the question based ONLY on the provided context
2. If the context doesn't contain enough information, clearly state this limitation
3. Cite specific sources using the format [Source X] where X is the source number
4. Provide specific numbers, dates, and facts when available
5. If comparing companies or time periods, be explicit about the data sources
6. Keep the answer focused and relevant to the question

ANSWER:"""
        
        return prompt
    
    def _extract_sources(self, context_chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract source information from context chunks"""
        sources = []
        
        for i, chunk in enumerate(context_chunks, 1):
            metadata = chunk.get("metadata", {})
            
            source = {
                "source_id": i,
                "ticker": metadata.get("ticker", "Unknown"),
                "filing_type": metadata.get("filing_type", "Unknown"),
                "filing_date": metadata.get("filing_date", "Unknown"),
                "section_name": metadata.get("section_name", "Unknown"),
                "accession_number": metadata.get("accession_number", "Unknown"),
                "has_financial_data": metadata.get("has_financial_data", False),
                "score": chunk.get("score", 0.0)
            }
            
            sources.append(source)
        
        return sources
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """Get list of available models"""
        return [
            {
                "key": key,
                "name": info["name"],
                "description": info["description"],
                "max_tokens": info["max_tokens"],
                "context_window": info["context_window"]
            }
            for key, info in self.models.items()
        ]
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get service status and statistics"""
        return {
            "service": "openrouter",
            "api_key_configured": bool(self.api_key),
            "primary_model": self.models[self.primary_model]["name"],
            "fallback_model": self.models[self.fallback_model]["name"],
            "total_requests": self.request_count,
            "total_errors": self.error_count,
            "last_error": self.last_error,
            "available_models": len(self.models),
            "base_url": self.base_url
        }
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()


# Global LLM service instance
llm_service = OpenRouterLLMService()
