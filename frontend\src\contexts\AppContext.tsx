/**
 * Application Context and State Management
 * Phase 6: Frontend Development - Task 6.2
 */

import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { QueryClient } from '@tanstack/react-query';
import { QueryFilters, QueryResponse, Company } from '../types/api';

// Application State Interface
interface AppState {
  // UI State
  theme: 'light' | 'dark';
  sidebarOpen: boolean;
  
  // User Preferences
  preferences: {
    defaultMaxChunks: number;
    defaultModel: 'gemma' | 'deepseek' | '';
    includeSources: boolean;
    includeConfidence: boolean;
    useCache: boolean;
    autoSave: boolean;
  };
  
  // Query State
  currentQuery: string;
  queryHistory: string[];
  bookmarkedQueries: string[];
  filters: QueryFilters;
  
  // Session State
  sessionId: string;
  userId: string;
  
  // Error State
  globalError: string | null;
  
  // Feature Flags
  features: {
    batchProcessing: boolean;
    exportFunctionality: boolean;
    advancedFilters: boolean;
    queryHistory: boolean;
  };
}

// Action Types
type AppAction =
  | { type: 'SET_THEME'; payload: 'light' | 'dark' }
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'SET_PREFERENCES'; payload: Partial<AppState['preferences']> }
  | { type: 'SET_CURRENT_QUERY'; payload: string }
  | { type: 'ADD_TO_HISTORY'; payload: string }
  | { type: 'CLEAR_HISTORY' }
  | { type: 'ADD_BOOKMARK'; payload: string }
  | { type: 'REMOVE_BOOKMARK'; payload: string }
  | { type: 'SET_FILTERS'; payload: QueryFilters }
  | { type: 'CLEAR_FILTERS' }
  | { type: 'SET_SESSION_ID'; payload: string }
  | { type: 'SET_USER_ID'; payload: string }
  | { type: 'SET_GLOBAL_ERROR'; payload: string | null }
  | { type: 'TOGGLE_FEATURE'; payload: { feature: keyof AppState['features']; enabled: boolean } }
  | { type: 'RESET_STATE' }
  | { type: 'LOAD_STATE'; payload: Partial<AppState> };

// Initial State
const initialState: AppState = {
  theme: 'light',
  sidebarOpen: false,
  preferences: {
    defaultMaxChunks: 5,
    defaultModel: '',
    includeSources: true,
    includeConfidence: true,
    useCache: true,
    autoSave: true,
  },
  currentQuery: '',
  queryHistory: [],
  bookmarkedQueries: [],
  filters: {},
  sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  userId: 'anonymous_user',
  globalError: null,
  features: {
    batchProcessing: true,
    exportFunctionality: true,
    advancedFilters: true,
    queryHistory: true,
  },
};

// Reducer
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_THEME':
      return { ...state, theme: action.payload };
      
    case 'TOGGLE_SIDEBAR':
      return { ...state, sidebarOpen: !state.sidebarOpen };
      
    case 'SET_PREFERENCES':
      return {
        ...state,
        preferences: { ...state.preferences, ...action.payload },
      };
      
    case 'SET_CURRENT_QUERY':
      return { ...state, currentQuery: action.payload };
      
    case 'ADD_TO_HISTORY':
      return {
        ...state,
        queryHistory: [
          action.payload,
          ...state.queryHistory.filter(q => q !== action.payload),
        ].slice(0, 50), // Keep only last 50 queries
      };
      
    case 'CLEAR_HISTORY':
      return { ...state, queryHistory: [] };
      
    case 'ADD_BOOKMARK':
      return {
        ...state,
        bookmarkedQueries: [
          action.payload,
          ...state.bookmarkedQueries.filter(q => q !== action.payload),
        ].slice(0, 100), // Keep only last 100 bookmarks
      };
      
    case 'REMOVE_BOOKMARK':
      return {
        ...state,
        bookmarkedQueries: state.bookmarkedQueries.filter(q => q !== action.payload),
      };
      
    case 'SET_FILTERS':
      return { ...state, filters: action.payload };
      
    case 'CLEAR_FILTERS':
      return { ...state, filters: {} };
      
    case 'SET_SESSION_ID':
      return { ...state, sessionId: action.payload };
      
    case 'SET_USER_ID':
      return { ...state, userId: action.payload };
      
    case 'SET_GLOBAL_ERROR':
      return { ...state, globalError: action.payload };
      
    case 'TOGGLE_FEATURE':
      return {
        ...state,
        features: {
          ...state.features,
          [action.payload.feature]: action.payload.enabled,
        },
      };
      
    case 'RESET_STATE':
      return { ...initialState, sessionId: state.sessionId, userId: state.userId };
      
    case 'LOAD_STATE':
      return { ...state, ...action.payload };
      
    default:
      return state;
  }
};

// Context Interface
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  
  // Convenience methods
  setTheme: (theme: 'light' | 'dark') => void;
  toggleSidebar: () => void;
  updatePreferences: (preferences: Partial<AppState['preferences']>) => void;
  setCurrentQuery: (query: string) => void;
  addToHistory: (query: string) => void;
  clearHistory: () => void;
  addBookmark: (query: string) => void;
  removeBookmark: (query: string) => void;
  setFilters: (filters: QueryFilters) => void;
  clearFilters: () => void;
  setGlobalError: (error: string | null) => void;
  toggleFeature: (feature: keyof AppState['features'], enabled: boolean) => void;
  resetState: () => void;
  
  // Computed values
  isBookmarked: (query: string) => boolean;
  getRecentQueries: (limit?: number) => string[];
}

// Create Context
const AppContext = createContext<AppContextType | undefined>(undefined);

// Local Storage Keys
const STORAGE_KEYS = {
  APP_STATE: 'sec_qa_app_state',
  PREFERENCES: 'sec_qa_preferences',
  QUERY_HISTORY: 'sec_qa_query_history',
  BOOKMARKS: 'sec_qa_bookmarks',
};

// Context Provider
export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Load state from localStorage on mount
  useEffect(() => {
    try {
      const savedPreferences = localStorage.getItem(STORAGE_KEYS.PREFERENCES);
      const savedHistory = localStorage.getItem(STORAGE_KEYS.QUERY_HISTORY);
      const savedBookmarks = localStorage.getItem(STORAGE_KEYS.BOOKMARKS);

      if (savedPreferences) {
        const preferences = JSON.parse(savedPreferences);
        dispatch({ type: 'SET_PREFERENCES', payload: preferences });
      }

      if (savedHistory) {
        const history = JSON.parse(savedHistory);
        dispatch({ type: 'LOAD_STATE', payload: { queryHistory: history } });
      }

      if (savedBookmarks) {
        const bookmarks = JSON.parse(savedBookmarks);
        dispatch({ type: 'LOAD_STATE', payload: { bookmarkedQueries: bookmarks } });
      }
    } catch (error) {
      console.error('Failed to load state from localStorage:', error);
    }
  }, []);

  // Save state to localStorage when it changes
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEYS.PREFERENCES, JSON.stringify(state.preferences));
    } catch (error) {
      console.error('Failed to save preferences to localStorage:', error);
    }
  }, [state.preferences]);

  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEYS.QUERY_HISTORY, JSON.stringify(state.queryHistory));
    } catch (error) {
      console.error('Failed to save query history to localStorage:', error);
    }
  }, [state.queryHistory]);

  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEYS.BOOKMARKS, JSON.stringify(state.bookmarkedQueries));
    } catch (error) {
      console.error('Failed to save bookmarks to localStorage:', error);
    }
  }, [state.bookmarkedQueries]);

  // Convenience methods
  const setTheme = useCallback((theme: 'light' | 'dark') => {
    dispatch({ type: 'SET_THEME', payload: theme });
  }, []);

  const toggleSidebar = useCallback(() => {
    dispatch({ type: 'TOGGLE_SIDEBAR' });
  }, []);

  const updatePreferences = useCallback((preferences: Partial<AppState['preferences']>) => {
    dispatch({ type: 'SET_PREFERENCES', payload: preferences });
  }, []);

  const setCurrentQuery = useCallback((query: string) => {
    dispatch({ type: 'SET_CURRENT_QUERY', payload: query });
  }, []);

  const addToHistory = useCallback((query: string) => {
    if (query.trim() && state.features.queryHistory) {
      dispatch({ type: 'ADD_TO_HISTORY', payload: query.trim() });
    }
  }, [state.features.queryHistory]);

  const clearHistory = useCallback(() => {
    dispatch({ type: 'CLEAR_HISTORY' });
  }, []);

  const addBookmark = useCallback((query: string) => {
    if (query.trim()) {
      dispatch({ type: 'ADD_BOOKMARK', payload: query.trim() });
    }
  }, []);

  const removeBookmark = useCallback((query: string) => {
    dispatch({ type: 'REMOVE_BOOKMARK', payload: query });
  }, []);

  const setFilters = useCallback((filters: QueryFilters) => {
    dispatch({ type: 'SET_FILTERS', payload: filters });
  }, []);

  const clearFilters = useCallback(() => {
    dispatch({ type: 'CLEAR_FILTERS' });
  }, []);

  const setGlobalError = useCallback((error: string | null) => {
    dispatch({ type: 'SET_GLOBAL_ERROR', payload: error });
  }, []);

  const toggleFeature = useCallback((feature: keyof AppState['features'], enabled: boolean) => {
    dispatch({ type: 'TOGGLE_FEATURE', payload: { feature, enabled } });
  }, []);

  const resetState = useCallback(() => {
    dispatch({ type: 'RESET_STATE' });
  }, []);

  // Computed values
  const isBookmarked = useCallback((query: string) => {
    return state.bookmarkedQueries.includes(query.trim());
  }, [state.bookmarkedQueries]);

  const getRecentQueries = useCallback((limit: number = 10) => {
    return state.queryHistory.slice(0, limit);
  }, [state.queryHistory]);

  const contextValue: AppContextType = {
    state,
    dispatch,
    setTheme,
    toggleSidebar,
    updatePreferences,
    setCurrentQuery,
    addToHistory,
    clearHistory,
    addBookmark,
    removeBookmark,
    setFilters,
    clearFilters,
    setGlobalError,
    toggleFeature,
    resetState,
    isBookmarked,
    getRecentQueries,
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook to use the context
export const useAppContext = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};

// Selector hooks for specific state slices
export const useAppTheme = () => {
  const { state, setTheme } = useAppContext();
  return { theme: state.theme, setTheme };
};

export const useAppPreferences = () => {
  const { state, updatePreferences } = useAppContext();
  return { preferences: state.preferences, updatePreferences };
};

export const useQueryState = () => {
  const { state, setCurrentQuery, addToHistory, setFilters, clearFilters } = useAppContext();
  return {
    currentQuery: state.currentQuery,
    filters: state.filters,
    setCurrentQuery,
    addToHistory,
    setFilters,
    clearFilters,
  };
};

export const useBookmarks = () => {
  const { state, addBookmark, removeBookmark, isBookmarked } = useAppContext();
  return {
    bookmarkedQueries: state.bookmarkedQueries,
    addBookmark,
    removeBookmark,
    isBookmarked,
  };
};

export const useQueryHistory = () => {
  const { state, clearHistory, getRecentQueries } = useAppContext();
  return {
    queryHistory: state.queryHistory,
    clearHistory,
    getRecentQueries,
  };
};

export default AppContext;
