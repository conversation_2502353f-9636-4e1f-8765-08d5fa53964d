"""
Document Vectorizer Service
Integrates document processing pipeline with embedding generation and vector storage
"""

import asyncio
from typing import List, Dict, Any, Optional
import structlog
from datetime import datetime

from app.services.sec_api import CompanyManager
from app.services.document_parser import SECDocumentParser
from app.services.document_chunker import DocumentChunker
from app.services.embedding_service import embedding_service
from app.services.vector_storage import vector_storage

logger = structlog.get_logger()


class DocumentVectorizer:
    """
    Service that orchestrates the complete document processing and vectorization pipeline
    """
    
    def __init__(self):
        self.company_manager = CompanyManager()
        self.document_parser = SECDocumentParser()
        self.document_chunker = DocumentChunker()
        
        # Initialize vector storage with correct embedding dimension
        self._sync_embedding_dimension()
    
    def _sync_embedding_dimension(self):
        """Synchronize vector storage dimension with embedding service"""
        embedding_dim = embedding_service.get_embedding_dimension()
        vector_storage.update_dimension(embedding_dim)
        logger.info("Synchronized embedding dimensions", dimension=embedding_dim)
    
    async def process_filing(self, ticker: str, accession_number: str, 
                           filing_content: str, filing_type: str, 
                           filing_date: str) -> Dict[str, Any]:
        """
        Process a complete SEC filing through the vectorization pipeline
        
        Args:
            ticker: Company ticker symbol
            accession_number: SEC accession number
            filing_content: Raw HTML content of the filing
            filing_type: Type of filing (10-K, 10-Q, etc.)
            filing_date: Filing date
            
        Returns:
            Dictionary with processing results and statistics
        """
        logger.info("Processing filing for vectorization", 
                   ticker=ticker, 
                   filing_type=filing_type,
                   accession_number=accession_number)
        
        start_time = datetime.now()
        
        try:
            # Step 1: Parse document
            logger.info("Step 1: Parsing document")
            parsed_doc = self.document_parser.parse_document(
                html_content=filing_content,
                filing_type=filing_type,
                ticker=ticker,
                filing_date=filing_date
            )
            
            sections_count = len(parsed_doc['sections'])
            tables_count = len(parsed_doc['tables'])
            logger.info("Document parsed", sections=sections_count, tables=tables_count)
            
            # Step 2: Chunk document
            logger.info("Step 2: Chunking document")
            chunks = self.document_chunker.chunk_document(parsed_doc)
            chunks_count = len(chunks)
            logger.info("Document chunked", chunks=chunks_count)
            
            if not chunks:
                logger.warning("No chunks generated from document")
                return {
                    "success": False,
                    "error": "No chunks generated from document",
                    "ticker": ticker,
                    "accession_number": accession_number,
                    "processing_time": (datetime.now() - start_time).total_seconds()
                }
            
            # Step 3: Generate embeddings
            logger.info("Step 3: Generating embeddings")
            chunk_texts = [chunk['text'] for chunk in chunks]
            embeddings = await embedding_service.generate_embeddings(chunk_texts)
            
            if len(embeddings) != len(chunks):
                raise Exception(f"Embedding count mismatch: {len(embeddings)} vs {len(chunks)}")
            
            logger.info("Embeddings generated", count=len(embeddings))
            
            # Step 4: Prepare metadata for vector storage
            logger.info("Step 4: Preparing metadata for vector storage")
            vector_metadata = []
            
            for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                metadata = {
                    # Document identifiers
                    "ticker": ticker,
                    "accession_number": accession_number,
                    "filing_type": filing_type,
                    "filing_date": filing_date,
                    
                    # Chunk information
                    "chunk_id": i,
                    "chunk_size": len(chunk['text']),
                    "section_name": chunk['metadata'].get('section_name', 'unknown'),
                    "section_type": chunk['metadata'].get('section_type', 'unknown'),
                    
                    # Content metadata
                    "has_financial_data": chunk['metadata'].get('has_financial_data', False),
                    "key_phrases": chunk['metadata'].get('key_phrases', []),
                    "content_preview": chunk['text'][:200] + "..." if len(chunk['text']) > 200 else chunk['text'],
                    
                    # Processing metadata
                    "processed_at": datetime.now().isoformat(),
                    "embedding_service": embedding_service.get_service_status()['current_service'],
                    "embedding_dimension": len(embedding)
                }
                
                vector_metadata.append(metadata)
            
            # Step 5: Store vectors
            logger.info("Step 5: Storing vectors")
            vector_ids = await vector_storage.store_vectors(embeddings, vector_metadata)
            logger.info("Vectors stored", count=len(vector_ids))
            
            # Calculate processing statistics
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            result = {
                "success": True,
                "ticker": ticker,
                "accession_number": accession_number,
                "filing_type": filing_type,
                "filing_date": filing_date,
                
                # Processing results
                "sections_parsed": sections_count,
                "tables_parsed": tables_count,
                "chunks_created": chunks_count,
                "embeddings_generated": len(embeddings),
                "vectors_stored": len(vector_ids),
                
                # Service information
                "embedding_service": embedding_service.get_service_status()['current_service'],
                "vector_storage": vector_storage.get_storage_stats()['current_service'],
                "embedding_dimension": embedding_service.get_embedding_dimension(),
                
                # Performance metrics
                "processing_time_seconds": processing_time,
                "chunks_per_second": chunks_count / processing_time if processing_time > 0 else 0,
                "embeddings_per_second": len(embeddings) / processing_time if processing_time > 0 else 0,
                
                # Vector IDs for reference
                "vector_ids": vector_ids[:10],  # First 10 IDs for reference
                "total_vector_ids": len(vector_ids)
            }
            
            logger.info("Filing processing completed successfully", 
                       ticker=ticker,
                       chunks=chunks_count,
                       vectors=len(vector_ids),
                       processing_time=processing_time)
            
            return result
            
        except Exception as e:
            logger.error("Filing processing failed", 
                        ticker=ticker,
                        accession_number=accession_number,
                        error=str(e))
            
            return {
                "success": False,
                "error": str(e),
                "ticker": ticker,
                "accession_number": accession_number,
                "filing_type": filing_type,
                "processing_time": (datetime.now() - start_time).total_seconds()
            }
    
    async def process_multiple_filings(self, filing_requests: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """
        Process multiple filings in batch
        
        Args:
            filing_requests: List of filing request dictionaries with keys:
                           ticker, accession_number, filing_content, filing_type, filing_date
                           
        Returns:
            List of processing results
        """
        logger.info("Processing multiple filings", count=len(filing_requests))
        
        results = []
        for i, request in enumerate(filing_requests):
            logger.info(f"Processing filing {i+1}/{len(filing_requests)}", 
                       ticker=request.get('ticker'),
                       filing_type=request.get('filing_type'))
            
            result = await self.process_filing(
                ticker=request['ticker'],
                accession_number=request['accession_number'],
                filing_content=request['filing_content'],
                filing_type=request['filing_type'],
                filing_date=request['filing_date']
            )
            
            results.append(result)
        
        # Calculate batch statistics
        successful = sum(1 for r in results if r['success'])
        failed = len(results) - successful
        total_chunks = sum(r.get('chunks_created', 0) for r in results if r['success'])
        total_vectors = sum(r.get('vectors_stored', 0) for r in results if r['success'])
        
        logger.info("Batch processing completed", 
                   total_filings=len(filing_requests),
                   successful=successful,
                   failed=failed,
                   total_chunks=total_chunks,
                   total_vectors=total_vectors)
        
        return results
    
    async def search_similar_content(self, query_text: str, top_k: int = 10, 
                                   filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Search for similar content using semantic search
        
        Args:
            query_text: Text to search for
            top_k: Number of results to return
            filters: Optional metadata filters
            
        Returns:
            List of similar content with metadata
        """
        logger.info("Searching for similar content", 
                   query_length=len(query_text),
                   top_k=top_k,
                   has_filters=filters is not None)
        
        try:
            # Generate embedding for query
            query_embeddings = await embedding_service.generate_embeddings([query_text])
            if not query_embeddings:
                raise Exception("Failed to generate query embedding")
            
            query_embedding = query_embeddings[0]
            
            # Search vectors
            results = await vector_storage.search_vectors(
                query_vector=query_embedding,
                top_k=top_k,
                filter_metadata=filters
            )
            
            logger.info("Similar content search completed", results_count=len(results))
            return results
            
        except Exception as e:
            logger.error("Similar content search failed", error=str(e))
            raise
    
    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get status of the complete vectorization pipeline"""
        return {
            "pipeline_ready": True,
            "embedding_service": embedding_service.get_service_status(),
            "vector_storage": vector_storage.get_storage_stats(),
            "services_initialized": {
                "company_manager": self.company_manager is not None,
                "document_parser": self.document_parser is not None,
                "document_chunker": self.document_chunker is not None,
                "embedding_service": embedding_service is not None,
                "vector_storage": vector_storage is not None
            }
        }


# Global document vectorizer instance
document_vectorizer = DocumentVectorizer()
