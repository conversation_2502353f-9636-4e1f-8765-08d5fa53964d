"""
Company management endpoints
"""

from fastapi import APIRouter
from pydantic import BaseModel
from typing import List, Optional
import structlog

logger = structlog.get_logger()
router = APIRouter()


class Company(BaseModel):
    """Company model"""
    ticker: str
    name: str
    sector: str
    industry: Optional[str] = None
    market_cap: Optional[float] = None
    filings_count: Optional[int] = 0


class CompanyList(BaseModel):
    """Company list response"""
    companies: List[Company]
    total: int


@router.get("/", response_model=CompanyList)
async def get_companies(
    search: Optional[str] = None,
    limit: int = 20
):
    """
    Get list of available companies from SEC database
    """
    try:
        from app.services.sec_api import company_manager

        if search:
            # Search for companies
            companies_data = await company_manager.search_companies(search, limit)
        else:
            # Get a sample of companies (first 20 from cache)
            await company_manager._load_companies()
            companies_data = list(company_manager.companies_cache.values())[:limit]

        companies = []
        for company_data in companies_data:
            company = Company(
                ticker=company_data.get('ticker', ''),
                name=company_data.get('title', ''),
                sector="Unknown",  # SEC data doesn't include sector
                industry=None,
                market_cap=None,
                filings_count=0  # Will be populated when we fetch filings
            )
            companies.append(company)

        logger.info("Retrieved companies from SEC", count=len(companies))
        return CompanyList(
            companies=companies,
            total=len(companies)
        )

    except Exception as e:
        logger.error("Failed to get companies from SEC", error=str(e))
        # Fallback to placeholder data
        placeholder_companies = [
            Company(
                ticker="AAPL",
                name="Apple Inc.",
                sector="Technology",
                industry="Consumer Electronics",
                market_cap=3000000000000,
                filings_count=0
            ),
            Company(
                ticker="MSFT",
                name="Microsoft Corporation",
                sector="Technology",
                industry="Software",
                market_cap=2800000000000,
                filings_count=0
            )
        ]

        return CompanyList(
            companies=placeholder_companies,
            total=len(placeholder_companies)
        )


@router.get("/{ticker}", response_model=Company)
async def get_company(ticker: str):
    """
    Get detailed information about a specific company from SEC database
    """
    try:
        from app.services.sec_api import company_manager

        company_data = await company_manager.get_company_info(ticker.upper())

        if not company_data:
            raise HTTPException(status_code=404, detail=f"Company not found for ticker: {ticker}")

        company = Company(
            ticker=company_data.get('ticker', ticker.upper()),
            name=company_data.get('title', ''),
            sector="Unknown",  # SEC data doesn't include sector
            industry=None,
            market_cap=None,
            filings_count=0  # Will be populated when we fetch filings
        )

        logger.info("Retrieved company info from SEC", ticker=ticker)
        return company

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get company from SEC", ticker=ticker, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve company information")
