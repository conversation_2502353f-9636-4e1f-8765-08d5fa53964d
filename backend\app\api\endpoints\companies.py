"""
Company management endpoints - Phase 5 Enhanced
"""

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
import structlog

logger = structlog.get_logger()
router = APIRouter()


class CompanyStats(BaseModel):
    """Company statistics model"""
    total_filings: int = Field(default=0, description="Total number of filings")
    filing_types: Dict[str, int] = Field(default_factory=dict, description="Count by filing type")
    latest_filing_date: Optional[str] = Field(default=None, description="Date of most recent filing")
    processed_filings: int = Field(default=0, description="Number of processed filings")
    vector_chunks: int = Field(default=0, description="Number of vector chunks stored")


class Company(BaseModel):
    """Enhanced company model"""
    ticker: str = Field(..., description="Stock ticker symbol")
    name: str = Field(..., description="Company name")
    sector: str = Field(default="Unknown", description="Business sector")
    industry: Optional[str] = Field(default=None, description="Industry classification")
    market_cap: Optional[float] = Field(default=None, description="Market capitalization in USD")
    cik: Optional[str] = Field(default=None, description="SEC Central Index Key")
    sic: Optional[str] = Field(default=None, description="Standard Industrial Classification")
    website: Optional[str] = Field(default=None, description="Company website")
    description: Optional[str] = Field(default=None, description="Company description")
    stats: CompanyStats = Field(default_factory=CompanyStats, description="Filing statistics")
    supported_for_qa: bool = Field(default=False, description="Available for Q&A queries")
    last_updated: Optional[str] = Field(default=None, description="Last data update timestamp")


class CompanyList(BaseModel):
    """Enhanced company list response"""
    companies: List[Company]
    total: int
    page: int = Field(default=1, description="Current page number")
    page_size: int = Field(default=20, description="Items per page")
    has_next: bool = Field(default=False, description="Whether there are more pages")
    supported_companies: int = Field(default=0, description="Number of companies with Q&A support")
    total_filings: int = Field(default=0, description="Total filings across all companies")


class CompanySearchRequest(BaseModel):
    """Company search request model"""
    query: Optional[str] = Field(default=None, description="Search query (name or ticker)")
    sector: Optional[str] = Field(default=None, description="Filter by sector")
    has_qa_support: Optional[bool] = Field(default=None, description="Filter by Q&A support")
    min_filings: Optional[int] = Field(default=None, description="Minimum number of filings")
    filing_types: Optional[List[str]] = Field(default=None, description="Filter by filing types")


@router.get("/", response_model=CompanyList)
async def get_companies(
    search: Optional[str] = Query(None, description="Search by company name or ticker"),
    sector: Optional[str] = Query(None, description="Filter by sector"),
    has_qa_support: Optional[bool] = Query(None, description="Filter by Q&A support"),
    min_filings: Optional[int] = Query(None, description="Minimum number of filings"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page")
):
    """
    Get list of available companies with enhanced filtering and pagination

    This endpoint provides comprehensive company information including:
    - Basic company details (ticker, name, sector)
    - Filing statistics and Q&A support status
    - Pagination and advanced filtering options
    """
    try:
        # Get supported companies from query engine
        from app.services.query_engine import query_engine
        engine_status = query_engine.get_engine_status()
        supported_tickers = set(engine_status.get("companies", []))

        # Get vector storage statistics
        from app.services.vector_storage import vector_storage
        vector_stats = vector_storage.get_storage_stats()

        # Build comprehensive company list
        companies = await _build_company_list(supported_tickers, vector_stats)

        # Apply filters
        filtered_companies = _apply_company_filters(
            companies, search, sector, has_qa_support, min_filings
        )

        # Apply pagination
        total_companies = len(filtered_companies)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_companies = filtered_companies[start_idx:end_idx]

        # Calculate summary statistics
        supported_count = sum(1 for c in filtered_companies if c.supported_for_qa)
        total_filings = sum(c.stats.total_filings for c in filtered_companies)

        logger.info("Retrieved companies list",
                   total=total_companies,
                   page=page,
                   page_size=page_size,
                   supported_count=supported_count)

        return CompanyList(
            companies=paginated_companies,
            total=total_companies,
            page=page,
            page_size=page_size,
            has_next=end_idx < total_companies,
            supported_companies=supported_count,
            total_filings=total_filings
        )

    except Exception as e:
        logger.error("Failed to get companies", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve companies")


async def _build_company_list(supported_tickers: set, vector_stats: dict) -> List[Company]:
    """Build comprehensive company list with statistics"""
    companies = []

    # Enhanced company data with real information
    company_data = {
        "AAPL": {
            "name": "Apple Inc.",
            "sector": "Technology",
            "industry": "Consumer Electronics",
            "market_cap": 3000000000000,
            "cik": "0000320193",
            "sic": "3571",
            "website": "https://www.apple.com",
            "description": "Designs, manufactures, and markets smartphones, personal computers, tablets, wearables, and accessories."
        },
        "MSFT": {
            "name": "Microsoft Corporation",
            "sector": "Technology",
            "industry": "Software",
            "market_cap": 2800000000000,
            "cik": "**********",
            "sic": "7372",
            "website": "https://www.microsoft.com",
            "description": "Develops, licenses, and supports software, services, devices, and solutions worldwide."
        },
        "GOOGL": {
            "name": "Alphabet Inc.",
            "sector": "Technology",
            "industry": "Internet Services",
            "market_cap": 1700000000000,
            "cik": "**********",
            "sic": "7370",
            "website": "https://abc.xyz",
            "description": "Provides online advertising services and cloud computing services."
        },
        "AMZN": {
            "name": "Amazon.com Inc.",
            "sector": "Consumer Discretionary",
            "industry": "E-commerce",
            "market_cap": 1500000000000,
            "cik": "**********",
            "sic": "5961",
            "website": "https://www.amazon.com",
            "description": "Offers a range of products and services through its websites."
        },
        "TSLA": {
            "name": "Tesla Inc.",
            "sector": "Consumer Discretionary",
            "industry": "Automotive",
            "market_cap": 800000000000,
            "cik": "0001318605",
            "sic": "3711",
            "website": "https://www.tesla.com",
            "description": "Designs, develops, manufactures, and sells electric vehicles and energy storage systems."
        }
    }

    # Add all supported companies
    all_tickers = supported_tickers.union(set(company_data.keys()))

    for ticker in sorted(all_tickers):
        data = company_data.get(ticker, {})

        # Get filing statistics from vector storage
        stats = _get_company_stats(ticker, vector_stats)

        company = Company(
            ticker=ticker,
            name=data.get("name", f"{ticker} Corporation"),
            sector=data.get("sector", "Unknown"),
            industry=data.get("industry"),
            market_cap=data.get("market_cap"),
            cik=data.get("cik"),
            sic=data.get("sic"),
            website=data.get("website"),
            description=data.get("description"),
            stats=stats,
            supported_for_qa=ticker in supported_tickers,
            last_updated=datetime.now().isoformat()
        )
        companies.append(company)

    return companies


def _get_company_stats(ticker: str, vector_stats: dict) -> CompanyStats:
    """Get filing statistics for a company"""
    try:
        from app.services.vector_storage import vector_storage

        # Count filings in vector storage
        metadata = vector_storage.faiss_metadata or {}
        company_chunks = [
            chunk for chunk in metadata.values()
            if chunk.get("ticker") == ticker
        ]

        # Count by filing type
        filing_types = {}
        for chunk in company_chunks:
            filing_type = chunk.get("filing_type", "Unknown")
            filing_types[filing_type] = filing_types.get(filing_type, 0) + 1

        # Get latest filing date
        latest_date = None
        for chunk in company_chunks:
            chunk_date = chunk.get("filing_date")
            if chunk_date and (not latest_date or chunk_date > latest_date):
                latest_date = chunk_date

        return CompanyStats(
            total_filings=len(set(chunk.get("accession_number") for chunk in company_chunks if chunk.get("accession_number"))),
            filing_types=filing_types,
            latest_filing_date=latest_date,
            processed_filings=len(company_chunks),
            vector_chunks=len(company_chunks)
        )

    except Exception as e:
        logger.error("Failed to get company stats", ticker=ticker, error=str(e))
        return CompanyStats()


def _apply_company_filters(companies: List[Company], search: Optional[str],
                          sector: Optional[str], has_qa_support: Optional[bool],
                          min_filings: Optional[int]) -> List[Company]:
    """Apply filters to company list"""
    filtered = companies

    if search:
        search_lower = search.lower()
        filtered = [
            c for c in filtered
            if search_lower in c.ticker.lower() or search_lower in c.name.lower()
        ]

    if sector:
        filtered = [c for c in filtered if c.sector.lower() == sector.lower()]

    if has_qa_support is not None:
        filtered = [c for c in filtered if c.supported_for_qa == has_qa_support]

    if min_filings is not None:
        filtered = [c for c in filtered if c.stats.total_filings >= min_filings]

    return filtered


@router.get("/{ticker}", response_model=Company)
async def get_company(ticker: str):
    """
    Get detailed information about a specific company

    Returns comprehensive company information including:
    - Basic company details and financial information
    - Filing statistics and processing status
    - Q&A support availability
    """
    ticker = ticker.upper()

    try:
        # Get supported companies
        from app.services.query_engine import query_engine
        engine_status = query_engine.get_engine_status()
        supported_tickers = set(engine_status.get("companies", []))

        # Get vector storage statistics
        from app.services.vector_storage import vector_storage
        vector_stats = vector_storage.get_storage_stats()

        # Build company list and find the requested company
        companies = await _build_company_list(supported_tickers, vector_stats)
        company = next((c for c in companies if c.ticker == ticker), None)

        if not company:
            raise HTTPException(status_code=404, detail=f"Company not found: {ticker}")

        logger.info("Retrieved company details", ticker=ticker, supported=company.supported_for_qa)
        return company

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get company details", ticker=ticker, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve company information")


@router.post("/search", response_model=CompanyList)
async def search_companies(request: CompanySearchRequest):
    """
    Advanced company search with multiple filters

    Supports complex filtering by:
    - Text search (name or ticker)
    - Sector and industry classification
    - Q&A support availability
    - Filing count thresholds
    - Specific filing types
    """
    try:
        # Get supported companies
        from app.services.query_engine import query_engine
        engine_status = query_engine.get_engine_status()
        supported_tickers = set(engine_status.get("companies", []))

        # Get vector storage statistics
        from app.services.vector_storage import vector_storage
        vector_stats = vector_storage.get_storage_stats()

        # Build company list
        companies = await _build_company_list(supported_tickers, vector_stats)

        # Apply filters
        filtered_companies = _apply_company_filters(
            companies, request.query, request.sector,
            request.has_qa_support, request.min_filings
        )

        # Additional filtering by filing types
        if request.filing_types:
            filtered_companies = [
                c for c in filtered_companies
                if any(ft in c.stats.filing_types for ft in request.filing_types)
            ]

        # Calculate summary statistics
        supported_count = sum(1 for c in filtered_companies if c.supported_for_qa)
        total_filings = sum(c.stats.total_filings for c in filtered_companies)

        logger.info("Company search completed",
                   query=request.query,
                   results=len(filtered_companies),
                   supported_count=supported_count)

        return CompanyList(
            companies=filtered_companies,
            total=len(filtered_companies),
            page=1,
            page_size=len(filtered_companies),
            has_next=False,
            supported_companies=supported_count,
            total_filings=total_filings
        )

    except Exception as e:
        logger.error("Company search failed", error=str(e))
        raise HTTPException(status_code=500, detail="Company search failed")


@router.get("/{ticker}/filings")
async def get_company_filings_summary(ticker: str):
    """
    Get filing summary for a specific company

    Returns detailed filing statistics and recent filing information
    for the specified company.
    """
    ticker = ticker.upper()

    try:
        from app.services.vector_storage import vector_storage

        # Get company filings from vector storage
        metadata = vector_storage.faiss_metadata or {}
        company_chunks = [
            chunk for chunk in metadata.values()
            if chunk.get("ticker") == ticker
        ]

        if not company_chunks:
            raise HTTPException(status_code=404, detail=f"No filings found for {ticker}")

        # Group by filing
        filings_map = {}
        for chunk in company_chunks:
            accession = chunk.get("accession_number", "unknown")
            if accession not in filings_map:
                filings_map[accession] = {
                    "accession_number": accession,
                    "ticker": chunk.get("ticker"),
                    "filing_type": chunk.get("filing_type"),
                    "filing_date": chunk.get("filing_date"),
                    "sections": [],
                    "chunks_count": 0,
                    "has_financial_data": False
                }

            filings_map[accession]["sections"].append(chunk.get("section_name"))
            filings_map[accession]["chunks_count"] += 1
            if chunk.get("has_financial_data"):
                filings_map[accession]["has_financial_data"] = True

        # Convert to list and sort by date
        filings = list(filings_map.values())
        filings.sort(key=lambda x: x.get("filing_date", ""), reverse=True)

        # Calculate statistics
        filing_types = {}
        for filing in filings:
            ft = filing.get("filing_type", "Unknown")
            filing_types[ft] = filing_types.get(ft, 0) + 1

        logger.info("Retrieved company filings summary",
                   ticker=ticker, filings_count=len(filings))

        return {
            "ticker": ticker,
            "total_filings": len(filings),
            "total_chunks": len(company_chunks),
            "filing_types": filing_types,
            "latest_filing_date": filings[0].get("filing_date") if filings else None,
            "filings": filings[:10],  # Return latest 10 filings
            "has_qa_support": len(company_chunks) > 0
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get company filings summary", ticker=ticker, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve filing summary")
