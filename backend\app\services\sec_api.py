"""
SEC EDGAR API Client
Handles interactions with the SEC EDGAR database
"""

import asyncio
import aiohttp
import time
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import structlog
from urllib.parse import urljoin

from app.core.config import settings

logger = structlog.get_logger()


class RateLimiter:
    """Rate limiter for SEC API compliance"""
    
    def __init__(self, requests_per_second: float = 10):
        self.requests_per_second = requests_per_second
        self.min_interval = 1.0 / requests_per_second
        self.last_request_time = 0
        
    async def wait_if_needed(self):
        """Wait if necessary to comply with rate limits"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_interval:
            wait_time = self.min_interval - time_since_last
            logger.debug("Rate limiting: waiting", wait_time=wait_time)
            await asyncio.sleep(wait_time)
        
        self.last_request_time = time.time()


class SECAPIClient:
    """SEC EDGAR API client with rate limiting and error handling"""
    
    def __init__(self):
        self.base_url = settings.SEC_API_BASE_URL
        self.user_agent = settings.SEC_API_USER_AGENT
        self.rate_limiter = RateLimiter(requests_per_second=10)  # SEC allows 10 requests/second
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Common headers required by SEC
        self.headers = {
            'User-Agent': self.user_agent,
            'Accept-Encoding': 'gzip, deflate',
            'Host': 'data.sec.gov'
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(headers=self.headers)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Make a rate-limited request to the SEC API"""
        await self.rate_limiter.wait_if_needed()
        
        url = urljoin(self.base_url, endpoint)
        
        try:
            logger.debug("Making SEC API request", url=url, params=params)
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.debug("SEC API request successful", status=response.status)
                    return data
                else:
                    error_text = await response.text()
                    logger.error("SEC API request failed", 
                               status=response.status, 
                               error=error_text)
                    raise Exception(f"SEC API request failed: {response.status} - {error_text}")
                    
        except Exception as e:
            logger.error("SEC API request error", error=str(e))
            raise
    
    async def get_company_tickers(self) -> Dict[str, Any]:
        """Get company tickers from bulk submissions data (fallback method)"""
        try:
            # Since the company_tickers.json endpoint is not available,
            # we'll use a predefined list of major companies for now
            # In a production system, you would download the bulk submissions.zip
            # and extract company information from there

            major_companies = {
                "0": {"cik_str": "320193", "ticker": "AAPL", "title": "Apple Inc."},
                "1": {"cik_str": "789019", "ticker": "MSFT", "title": "Microsoft Corporation"},
                "2": {"cik_str": "1652044", "ticker": "GOOGL", "title": "Alphabet Inc."},
                "3": {"cik_str": "1018724", "ticker": "AMZN", "title": "Amazon.com Inc."},
                "4": {"cik_str": "1318605", "ticker": "TSLA", "title": "Tesla Inc."},
                "5": {"cik_str": "1067983", "ticker": "BRK-B", "title": "Berkshire Hathaway Inc."},
                "6": {"cik_str": "1326801", "ticker": "META", "title": "Meta Platforms Inc."},
                "7": {"cik_str": "1045810", "ticker": "NVDA", "title": "NVIDIA Corporation"},
                "8": {"cik_str": "19617", "ticker": "JPM", "title": "JPMorgan Chase & Co."},
                "9": {"cik_str": "886982", "ticker": "JNJ", "title": "Johnson & Johnson"},
                "10": {"cik_str": "1559720", "ticker": "V", "title": "Visa Inc."},
                "11": {"cik_str": "1090872", "ticker": "PG", "title": "Procter & Gamble Company"},
                "12": {"cik_str": "1467373", "ticker": "UNH", "title": "UnitedHealth Group Incorporated"},
                "13": {"cik_str": "1090727", "ticker": "HD", "title": "Home Depot Inc."},
                "14": {"cik_str": "1551152", "ticker": "MA", "title": "Mastercard Incorporated"}
            }

            logger.info("Retrieved company tickers (predefined list)", count=len(major_companies))
            return major_companies
        except Exception as e:
            logger.error("Failed to get company tickers", error=str(e))
            raise
    
    async def get_company_concept(self, cik: str, taxonomy: str, tag: str) -> Dict[str, Any]:
        """Get company concept data"""
        try:
            # Pad CIK to 10 digits
            cik_padded = str(cik).zfill(10)
            endpoint = f"/api/xbrl/companyconcept/CIK{cik_padded}/{taxonomy}/{tag}.json"
            data = await self._make_request(endpoint)
            logger.info("Retrieved company concept", cik=cik, taxonomy=taxonomy, tag=tag)
            return data
        except Exception as e:
            logger.error("Failed to get company concept", cik=cik, error=str(e))
            raise
    
    async def get_company_facts(self, cik: str) -> Dict[str, Any]:
        """Get all company facts for a CIK"""
        try:
            # Pad CIK to 10 digits
            cik_padded = str(cik).zfill(10)
            endpoint = f"/api/xbrl/companyfacts/CIK{cik_padded}.json"
            data = await self._make_request(endpoint)
            logger.info("Retrieved company facts", cik=cik)
            return data
        except Exception as e:
            logger.error("Failed to get company facts", cik=cik, error=str(e))
            raise
    
    async def get_submissions(self, cik: str) -> Dict[str, Any]:
        """Get company submissions (filings) for a CIK"""
        try:
            # Pad CIK to 10 digits
            cik_padded = str(cik).zfill(10)
            endpoint = f"/submissions/CIK{cik_padded}.json"
            data = await self._make_request(endpoint)
            logger.info("Retrieved company submissions", cik=cik, 
                       filings_count=len(data.get('filings', {}).get('recent', {}).get('accessionNumber', [])))
            return data
        except Exception as e:
            logger.error("Failed to get company submissions", cik=cik, error=str(e))
            raise
    
    async def get_filing_document(self, accession_number: str, cik: str, document_name: str) -> str:
        """Get the actual filing document content"""
        try:
            # Remove dashes from accession number for URL
            accession_clean = accession_number.replace('-', '')
            cik_padded = str(cik).zfill(10)
            
            endpoint = f"/Archives/edgar/data/{cik}/{accession_clean}/{document_name}"
            
            # For document content, we need to handle text response
            await self.rate_limiter.wait_if_needed()
            url = urljoin(self.base_url, endpoint)
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    content = await response.text()
                    logger.info("Retrieved filing document", 
                               accession_number=accession_number,
                               document_name=document_name,
                               content_length=len(content))
                    return content
                else:
                    error_text = await response.text()
                    logger.error("Failed to get filing document", 
                               status=response.status, 
                               error=error_text)
                    raise Exception(f"Failed to get filing document: {response.status}")
                    
        except Exception as e:
            logger.error("Failed to get filing document", 
                        accession_number=accession_number, 
                        error=str(e))
            raise


class CompanyManager:
    """Manages company information and ticker lookups"""
    
    def __init__(self):
        self.companies_cache: Optional[Dict[str, Any]] = None
        self.ticker_to_cik: Optional[Dict[str, str]] = None
        self.cik_to_ticker: Optional[Dict[str, str]] = None
        self.last_update: Optional[datetime] = None
        self.cache_duration = timedelta(hours=24)  # Cache for 24 hours
    
    async def _load_companies(self, force_refresh: bool = False):
        """Load company data from SEC API"""
        if (not force_refresh and 
            self.companies_cache and 
            self.last_update and 
            datetime.now() - self.last_update < self.cache_duration):
            return
        
        try:
            async with SECAPIClient() as client:
                self.companies_cache = await client.get_company_tickers()
                
            # Build lookup dictionaries
            self.ticker_to_cik = {}
            self.cik_to_ticker = {}
            
            for company_data in self.companies_cache.values():
                ticker = company_data.get('ticker', '').upper()
                cik = str(company_data.get('cik_str', ''))
                
                if ticker and cik:
                    self.ticker_to_cik[ticker] = cik
                    self.cik_to_ticker[cik] = ticker
            
            self.last_update = datetime.now()
            logger.info("Loaded company data", 
                       companies_count=len(self.companies_cache),
                       tickers_count=len(self.ticker_to_cik))
            
        except Exception as e:
            logger.error("Failed to load company data", error=str(e))
            raise
    
    async def get_cik_by_ticker(self, ticker: str) -> Optional[str]:
        """Get CIK by ticker symbol"""
        await self._load_companies()
        return self.ticker_to_cik.get(ticker.upper())
    
    async def get_ticker_by_cik(self, cik: str) -> Optional[str]:
        """Get ticker by CIK"""
        await self._load_companies()
        return self.cik_to_ticker.get(str(cik))
    
    async def get_company_info(self, ticker: str) -> Optional[Dict[str, Any]]:
        """Get complete company information by ticker"""
        await self._load_companies()
        
        cik = await self.get_cik_by_ticker(ticker)
        if not cik:
            return None
        
        # Find the company data
        for company_data in self.companies_cache.values():
            if str(company_data.get('cik_str', '')) == cik:
                return company_data
        
        return None
    
    async def search_companies(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search companies by name or ticker"""
        await self._load_companies()
        
        query_lower = query.lower()
        results = []
        
        for company_data in self.companies_cache.values():
            ticker = company_data.get('ticker', '').lower()
            title = company_data.get('title', '').lower()
            
            if (query_lower in ticker or 
                query_lower in title or 
                ticker.startswith(query_lower)):
                results.append(company_data)
                
                if len(results) >= limit:
                    break
        
        return results


class FilingManager:
    """Manages SEC filing operations"""
    
    def __init__(self):
        self.company_manager = CompanyManager()
    
    async def get_company_filings(self, ticker: str, 
                                filing_types: Optional[List[str]] = None,
                                limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent filings for a company"""
        try:
            cik = await self.company_manager.get_cik_by_ticker(ticker)
            if not cik:
                raise ValueError(f"Company not found for ticker: {ticker}")
            
            async with SECAPIClient() as client:
                submissions = await client.get_submissions(cik)
            
            # Extract recent filings
            recent_filings = submissions.get('filings', {}).get('recent', {})
            
            if not recent_filings:
                return []
            
            # Build filing list
            filings = []
            accession_numbers = recent_filings.get('accessionNumber', [])
            filing_dates = recent_filings.get('filingDate', [])
            forms = recent_filings.get('form', [])
            primary_documents = recent_filings.get('primaryDocument', [])
            
            for i in range(min(len(accession_numbers), limit * 3)):  # Get extra to filter
                form = forms[i] if i < len(forms) else ''
                
                # Filter by filing types if specified
                if filing_types and form not in filing_types:
                    continue
                
                filing = {
                    'ticker': ticker,
                    'cik': cik,
                    'accession_number': accession_numbers[i],
                    'filing_date': filing_dates[i] if i < len(filing_dates) else '',
                    'form': form,
                    'primary_document': primary_documents[i] if i < len(primary_documents) else '',
                    'url': self._build_filing_url(cik, accession_numbers[i], 
                                                primary_documents[i] if i < len(primary_documents) else '')
                }
                
                filings.append(filing)
                
                if len(filings) >= limit:
                    break
            
            logger.info("Retrieved company filings", 
                       ticker=ticker, 
                       filings_count=len(filings))
            return filings
            
        except Exception as e:
            logger.error("Failed to get company filings", ticker=ticker, error=str(e))
            raise
    
    async def get_filing_content(self, ticker: str, accession_number: str, 
                               document_name: str) -> str:
        """Get the content of a specific filing document"""
        try:
            cik = await self.company_manager.get_cik_by_ticker(ticker)
            if not cik:
                raise ValueError(f"Company not found for ticker: {ticker}")
            
            async with SECAPIClient() as client:
                content = await client.get_filing_document(accession_number, cik, document_name)
            
            logger.info("Retrieved filing content", 
                       ticker=ticker,
                       accession_number=accession_number,
                       content_length=len(content))
            return content
            
        except Exception as e:
            logger.error("Failed to get filing content", 
                        ticker=ticker, 
                        accession_number=accession_number, 
                        error=str(e))
            raise
    
    def _build_filing_url(self, cik: str, accession_number: str, document_name: str) -> str:
        """Build the URL for a filing document"""
        accession_clean = accession_number.replace('-', '')
        return f"{settings.SEC_API_BASE_URL}/Archives/edgar/data/{cik}/{accession_clean}/{document_name}"


# Global instances
company_manager = CompanyManager()
filing_manager = FilingManager()
