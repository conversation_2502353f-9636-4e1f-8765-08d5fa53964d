import asyncio
from app.services.sec_api import SECAPIClient

async def test():
    try:
        async with SECAPIClient() as client:
            tickers = await client.get_company_tickers()
            print(f'✅ Retrieved {len(tickers)} companies')
            
            # Test Apple specifically
            submissions = await client.get_submissions("320193")
            recent = submissions.get('filings', {}).get('recent', {})
            count = len(recent.get('accessionNumber', []))
            print(f'✅ Apple has {count} recent filings')
            
    except Exception as e:
        print(f'❌ Error: {str(e)}')

if __name__ == "__main__":
    asyncio.run(test())
