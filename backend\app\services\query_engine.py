"""
Query Processing Engine
Implements RAG (Retrieval-Augmented Generation) for SEC filings Q&A
"""

import re
import asyncio
from typing import List, Dict, Any, Optional, Tuple
import structlog
from datetime import datetime

from app.services.document_vectorizer import document_vectorizer
from app.services.llm_service import llm_service
from app.services.enhanced_query_parser import enhanced_parser
from app.services.hybrid_retrieval import hybrid_retrieval

logger = structlog.get_logger()


class QueryEngine:
    """
    Complete query processing engine implementing RAG for SEC filings
    """
    
    def __init__(self):
        self.supported_tickers = [
            "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "BRK-B", "META", 
            "NVDA", "JPM", "JNJ", "V", "PG", "UNH", "HD", "MA"
        ]
        
        self.filing_types = ["10-K", "10-Q", "8-K", "DEF 14A"]
        
        # Query classification patterns
        self.query_patterns = {
            "financial_performance": [
                r"revenue", r"sales", r"earnings", r"profit", r"income", r"financial performance",
                r"quarterly results", r"annual results", r"financial statements"
            ],
            "risk_factors": [
                r"risk", r"risks", r"risk factors", r"challenges", r"threats", r"uncertainties"
            ],
            "business_operations": [
                r"business", r"operations", r"strategy", r"products", r"services", r"market"
            ],
            "comparison": [
                r"compare", r"comparison", r"versus", r"vs", r"compared to", r"difference"
            ],
            "temporal": [
                r"quarter", r"year", r"Q1", r"Q2", r"Q3", r"Q4", r"2023", r"2024", r"2025",
                r"previous", r"last", r"current", r"recent"
            ]
        }
    
    async def process_query(self, query: str, max_chunks: int = 5, 
                          model_preference: str = None) -> Dict[str, Any]:
        """
        Process a user query using RAG approach
        
        Args:
            query: User's question
            max_chunks: Maximum number of context chunks to retrieve
            model_preference: Preferred LLM model ("gemma" or "deepseek")
            
        Returns:
            Complete query response with answer and metadata
        """
        logger.info("Processing user query", 
                   query_length=len(query),
                   max_chunks=max_chunks,
                   model_preference=model_preference)
        
        start_time = datetime.now()
        
        try:
            # Step 1: Parse and analyze query
            query_analysis = self._analyze_query(query)
            logger.info("Query analyzed", 
                       intent=query_analysis["intent"],
                       tickers=query_analysis["tickers"],
                       filing_types=query_analysis["filing_types"])
            
            # Step 2: Retrieve relevant context
            context_chunks = await self._retrieve_context(query, query_analysis, max_chunks)
            logger.info("Context retrieved", chunks_count=len(context_chunks))
            
            if not context_chunks:
                return self._create_no_context_response(query, query_analysis, start_time)
            
            # Step 3: Generate answer using LLM
            llm_result = await llm_service.generate_answer(
                query=query,
                context_chunks=context_chunks,
                model_preference=model_preference
            )
            
            # Step 4: Compile complete response
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            response = {
                "success": True,
                "query": query,
                "answer": llm_result["answer"],
                
                # Query analysis
                "query_analysis": query_analysis,
                
                # Context information
                "context_chunks_retrieved": len(context_chunks),
                "context_sources": self._summarize_sources(context_chunks),
                
                # LLM information
                "model_used": llm_result["model_used"],
                "model_key": llm_result["model_key"],
                "llm_response_time": llm_result["response_time_seconds"],
                "used_fallback": llm_result.get("used_fallback", False),
                
                # Sources and attribution
                "sources": llm_result["sources"],
                "source_attribution": self._create_source_attribution(context_chunks),
                
                # Performance metrics
                "total_processing_time": processing_time,
                "retrieval_time": processing_time - llm_result["response_time_seconds"],
                
                # Metadata
                "timestamp": datetime.now().isoformat(),
                "usage": llm_result.get("usage", {}),
                "confidence_indicators": self._assess_confidence(context_chunks, llm_result)
            }
            
            logger.info("Query processed successfully",
                       processing_time=processing_time,
                       answer_length=len(llm_result["answer"]),
                       sources_count=len(context_chunks))
            
            return response
            
        except Exception as e:
            logger.error("Query processing failed", query=query, error=str(e))
            
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            return {
                "success": False,
                "query": query,
                "error": str(e),
                "error_type": type(e).__name__,
                "processing_time": processing_time,
                "timestamp": datetime.now().isoformat()
            }
    
    def _analyze_query(self, query: str) -> Dict[str, Any]:
        """Enhanced query analysis using advanced NER and parsing"""
        try:
            # Use enhanced parser for comprehensive analysis
            enhanced_analysis = enhanced_parser.parse_query_enhanced(query)

            # Convert to legacy format for backward compatibility
            legacy_format = {
                "intent": enhanced_analysis["intent"]["primary"],
                "intent_scores": enhanced_analysis["intent"]["scores"],
                "tickers": [company["ticker"] for company in enhanced_analysis["companies"]],
                "filing_types": [ft["filing_type"] for ft in enhanced_analysis["filing_types"]],
                "time_references": self._extract_time_references_legacy(enhanced_analysis["date_info"]),
                "query_length": enhanced_analysis["query_length"],
                "word_count": enhanced_analysis["word_count"],
                "has_comparison": enhanced_analysis["comparison_info"]["has_comparison"],
                "has_numbers": len(enhanced_analysis["numbers"]) > 0,
                "question_type": enhanced_analysis["question_type"],

                # Enhanced fields
                "enhanced_analysis": enhanced_analysis,
                "companies_detailed": enhanced_analysis["companies"],
                "filing_types_detailed": enhanced_analysis["filing_types"],
                "date_info": enhanced_analysis["date_info"],
                "financial_metrics": enhanced_analysis["financial_metrics"],
                "comparison_info": enhanced_analysis["comparison_info"],
                "numbers": enhanced_analysis["numbers"],
                "complexity_score": enhanced_analysis["complexity_score"]
            }

            logger.info("Enhanced query analysis completed",
                       intent=legacy_format["intent"],
                       companies_found=len(legacy_format["tickers"]),
                       filing_types_found=len(legacy_format["filing_types"]),
                       complexity=legacy_format["complexity_score"])

            return legacy_format

        except Exception as e:
            logger.error("Enhanced query analysis failed, falling back to basic", error=str(e))
            # Fallback to basic analysis
            return self._analyze_query_basic(query)

    def _analyze_query_basic(self, query: str) -> Dict[str, Any]:
        """Basic query analysis (fallback method)"""
        query_lower = query.lower()

        # Extract tickers (use word boundaries to avoid false matches)
        tickers = []
        for ticker in self.supported_tickers:
            ticker_pattern = r'\b' + re.escape(ticker.lower()) + r'\b'
            ticker_alt_pattern = r'\b' + re.escape(ticker.replace("-", "").lower()) + r'\b'

            if re.search(ticker_pattern, query_lower) or re.search(ticker_alt_pattern, query_lower):
                tickers.append(ticker)

        # Extract filing types
        filing_types = []
        for filing_type in self.filing_types:
            if filing_type.lower() in query_lower:
                filing_types.append(filing_type)

        # Classify query intent
        intent_scores = {}
        for intent, patterns in self.query_patterns.items():
            score = sum(1 for pattern in patterns if re.search(pattern, query_lower))
            if score > 0:
                intent_scores[intent] = score

        primary_intent = max(intent_scores.keys(), key=lambda k: intent_scores[k]) if intent_scores else "general"

        # Extract time references
        time_references = []
        time_patterns = [
            r"Q[1-4]\s*20\d{2}", r"20\d{2}", r"quarter", r"year", r"fiscal",
            r"previous", r"last", r"current", r"recent"
        ]

        for pattern in time_patterns:
            matches = re.findall(pattern, query_lower)
            time_references.extend(matches)

        return {
            "intent": primary_intent,
            "intent_scores": intent_scores,
            "tickers": tickers,
            "filing_types": filing_types,
            "time_references": time_references,
            "query_length": len(query),
            "word_count": len(query.split()),
            "has_comparison": any(pattern in query_lower for pattern in ["compare", "vs", "versus", "difference"]),
            "has_numbers": bool(re.search(r'\d+', query)),
            "question_type": self._classify_question_type(query)
        }

    def _extract_time_references_legacy(self, date_info: Dict[str, Any]) -> List[str]:
        """Convert enhanced date info to legacy time references format"""
        time_refs = []

        # Add quarters
        for quarter in date_info.get("quarters", []):
            time_refs.append(quarter.get("formatted", ""))

        # Add years
        for year in date_info.get("years", []):
            time_refs.append(str(year.get("year", "")))

        # Add relative dates
        for rel_date in date_info.get("relative_dates", []):
            time_refs.append(rel_date.get("text", ""))

        return [ref for ref in time_refs if ref]  # Remove empty strings
    
    def _classify_question_type(self, query: str) -> str:
        """Classify the type of question"""
        query_lower = query.lower().strip()
        
        if query_lower.startswith(("what", "what's", "what is", "what are", "what was", "what were")):
            return "what"
        elif query_lower.startswith(("how", "how's", "how is", "how are", "how was", "how were", "how did", "how do")):
            return "how"
        elif query_lower.startswith(("why", "why is", "why are", "why was", "why were", "why did", "why do")):
            return "why"
        elif query_lower.startswith(("when", "when is", "when are", "when was", "when were", "when did", "when do")):
            return "when"
        elif query_lower.startswith(("where", "where is", "where are", "where was", "where were")):
            return "where"
        elif query_lower.startswith(("who", "who is", "who are", "who was", "who were")):
            return "who"
        elif query_lower.startswith(("which", "which is", "which are", "which was", "which were")):
            return "which"
        elif query_lower.endswith("?"):
            return "question"
        else:
            return "statement"
    
    async def _retrieve_context(self, query: str, query_analysis: Dict[str, Any],
                               max_chunks: int) -> List[Dict[str, Any]]:
        """Enhanced context retrieval using hybrid search (semantic + keyword)"""

        try:
            # Use hybrid retrieval system for better results
            results = await hybrid_retrieval.hybrid_search(
                query=query,
                query_analysis=query_analysis,
                max_results=max_chunks,
                semantic_weight=0.7  # 70% semantic, 30% keyword
            )

            # Enhance results with additional relevance scoring
            enhanced_results = []
            for result in results:
                enhanced_result = result.copy()

                # Calculate comprehensive relevance score
                relevance_score = self._calculate_relevance_score(result, query_analysis)
                enhanced_result["relevance_score"] = relevance_score

                # Add search method information
                enhanced_result["search_methods"] = {
                    "has_semantic": result.get("has_semantic", False),
                    "has_keyword": result.get("has_keyword", False),
                    "semantic_score": result.get("semantic_score", 0.0),
                    "keyword_score": result.get("keyword_score", 0.0),
                    "combined_score": result.get("combined_score", 0.0),
                    "matched_keywords": result.get("matched_keywords", [])
                }

                enhanced_results.append(enhanced_result)

            # Sort by relevance score (hybrid search already sorts, but we re-sort after enhancement)
            enhanced_results.sort(key=lambda x: x["relevance_score"], reverse=True)

            logger.info("Context retrieval completed",
                       results_count=len(enhanced_results),
                       hybrid_results=sum(1 for r in enhanced_results if r["search_methods"]["has_semantic"] and r["search_methods"]["has_keyword"]),
                       semantic_only=sum(1 for r in enhanced_results if r["search_methods"]["has_semantic"] and not r["search_methods"]["has_keyword"]),
                       keyword_only=sum(1 for r in enhanced_results if not r["search_methods"]["has_semantic"] and r["search_methods"]["has_keyword"]))

            return enhanced_results

        except Exception as e:
            logger.error("Enhanced context retrieval failed, falling back to basic", error=str(e))
            # Fallback to basic semantic search
            return await self._retrieve_context_basic(query, query_analysis, max_chunks)

    async def _retrieve_context_basic(self, query: str, query_analysis: Dict[str, Any],
                                     max_chunks: int) -> List[Dict[str, Any]]:
        """Basic context retrieval (fallback method)"""
        filters = {}

        # Filter by content type based on intent
        if query_analysis["intent"] == "financial_performance":
            filters["has_financial_data"] = True

        try:
            # Perform basic semantic search
            results = await document_vectorizer.search_similar_content(
                query_text=query,
                top_k=max_chunks,
                filters=filters if filters else None
            )

            # Enhance results with relevance scoring
            enhanced_results = []
            for result in results:
                enhanced_result = result.copy()
                enhanced_result["relevance_score"] = self._calculate_relevance_score(
                    result, query_analysis
                )
                enhanced_results.append(enhanced_result)

            # Sort by relevance score
            enhanced_results.sort(key=lambda x: x["relevance_score"], reverse=True)

            return enhanced_results

        except Exception as e:
            logger.error("Basic context retrieval failed", error=str(e))
            return []
    
    def _calculate_relevance_score(self, result: Dict[str, Any], 
                                 query_analysis: Dict[str, Any]) -> float:
        """Calculate relevance score for a search result"""
        base_score = result.get("score", 0.0)
        metadata = result.get("metadata", {})
        
        # Boost score based on query analysis
        relevance_multiplier = 1.0
        
        # Boost if ticker matches
        if query_analysis["tickers"]:
            result_ticker = metadata.get("ticker", "")
            if result_ticker in query_analysis["tickers"]:
                relevance_multiplier += 0.3
        
        # Boost if filing type matches
        if query_analysis["filing_types"]:
            result_filing_type = metadata.get("filing_type", "")
            if result_filing_type in query_analysis["filing_types"]:
                relevance_multiplier += 0.2
        
        # Boost based on intent matching
        section_name = metadata.get("section_name", "").lower()
        intent = query_analysis["intent"]
        
        if intent == "financial_performance" and "financial" in section_name:
            relevance_multiplier += 0.4
        elif intent == "risk_factors" and "risk" in section_name:
            relevance_multiplier += 0.4
        elif intent == "business_operations" and "business" in section_name:
            relevance_multiplier += 0.3
        
        # Boost if has financial data and query is about financials
        if (metadata.get("has_financial_data", False) and 
            intent == "financial_performance"):
            relevance_multiplier += 0.2
        
        return base_score * relevance_multiplier

    def _create_no_context_response(self, query: str, query_analysis: Dict[str, Any],
                                   start_time: datetime) -> Dict[str, Any]:
        """Create response when no relevant context is found"""
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()

        # Provide helpful guidance based on query analysis
        suggestions = []

        if query_analysis["tickers"]:
            suggestions.append(f"Try asking about these supported companies: {', '.join(self.supported_tickers)}")
        else:
            suggestions.append("Try including a specific company ticker (e.g., AAPL, MSFT, GOOGL)")

        if not query_analysis["filing_types"]:
            suggestions.append("Try specifying a filing type (10-K, 10-Q, 8-K, DEF 14A)")

        suggestions.append("Make sure your question is about SEC filings and financial information")

        return {
            "success": False,
            "query": query,
            "error": "No relevant SEC filing information found for your query",
            "error_type": "NoContextFound",
            "suggestions": suggestions,
            "query_analysis": query_analysis,
            "processing_time": processing_time,
            "timestamp": datetime.now().isoformat()
        }

    def _summarize_sources(self, context_chunks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Summarize the sources used for context"""
        if not context_chunks:
            return {}

        tickers = set()
        filing_types = set()
        sections = set()
        date_range = {"earliest": None, "latest": None}

        for chunk in context_chunks:
            metadata = chunk.get("metadata", {})

            if metadata.get("ticker"):
                tickers.add(metadata["ticker"])

            if metadata.get("filing_type"):
                filing_types.add(metadata["filing_type"])

            if metadata.get("section_name"):
                sections.add(metadata["section_name"])

            filing_date = metadata.get("filing_date")
            if filing_date:
                if not date_range["earliest"] or filing_date < date_range["earliest"]:
                    date_range["earliest"] = filing_date
                if not date_range["latest"] or filing_date > date_range["latest"]:
                    date_range["latest"] = filing_date

        return {
            "companies": list(tickers),
            "filing_types": list(filing_types),
            "sections": list(sections),
            "date_range": date_range,
            "total_sources": len(context_chunks)
        }

    def _create_source_attribution(self, context_chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create detailed source attribution for transparency"""
        attributions = []

        for i, chunk in enumerate(context_chunks, 1):
            metadata = chunk.get("metadata", {})

            attribution = {
                "source_number": i,
                "company": metadata.get("ticker", "Unknown"),
                "filing_type": metadata.get("filing_type", "Unknown"),
                "filing_date": metadata.get("filing_date", "Unknown"),
                "section": metadata.get("section_name", "Unknown"),
                "accession_number": metadata.get("accession_number", "Unknown"),
                "relevance_score": chunk.get("score", 0.0),
                "has_financial_data": metadata.get("has_financial_data", False),
                "content_preview": metadata.get("content_preview", "")[:100] + "..." if metadata.get("content_preview", "") else ""
            }

            attributions.append(attribution)

        return attributions

    def _assess_confidence(self, context_chunks: List[Dict[str, Any]],
                          llm_result: Dict[str, Any]) -> Dict[str, Any]:
        """Assess confidence in the answer based on various factors"""

        if not context_chunks:
            return {"overall": "low", "reasons": ["No relevant context found"]}

        confidence_factors = {
            "context_relevance": "medium",
            "source_diversity": "medium",
            "answer_specificity": "medium",
            "source_recency": "medium"
        }

        reasons = []

        # Assess context relevance
        avg_score = sum(chunk.get("score", 0) for chunk in context_chunks) / len(context_chunks)
        if avg_score > 0.8:
            confidence_factors["context_relevance"] = "high"
            reasons.append("High semantic similarity with source documents")
        elif avg_score < 0.3:
            confidence_factors["context_relevance"] = "low"
            reasons.append("Low semantic similarity with source documents")

        # Assess source diversity
        unique_sources = len(set(
            (chunk.get("metadata", {}).get("ticker", ""),
             chunk.get("metadata", {}).get("accession_number", ""))
            for chunk in context_chunks
        ))

        if unique_sources >= 3:
            confidence_factors["source_diversity"] = "high"
            reasons.append("Multiple diverse sources consulted")
        elif unique_sources == 1:
            confidence_factors["source_diversity"] = "low"
            reasons.append("Limited to single source")

        # Assess answer specificity
        answer_length = len(llm_result.get("answer", ""))
        if answer_length > 200:
            confidence_factors["answer_specificity"] = "high"
            reasons.append("Detailed and comprehensive answer")
        elif answer_length < 50:
            confidence_factors["answer_specificity"] = "low"
            reasons.append("Brief answer with limited detail")

        # Calculate overall confidence
        confidence_scores = {"high": 3, "medium": 2, "low": 1}
        avg_confidence = sum(confidence_scores[level] for level in confidence_factors.values()) / len(confidence_factors)

        if avg_confidence >= 2.5:
            overall = "high"
        elif avg_confidence >= 1.5:
            overall = "medium"
        else:
            overall = "low"

        return {
            "overall": overall,
            "factors": confidence_factors,
            "reasons": reasons,
            "context_score": avg_score,
            "source_count": len(context_chunks),
            "unique_sources": unique_sources
        }

    def get_engine_status(self) -> Dict[str, Any]:
        """Get query engine status and capabilities"""
        return {
            "engine_ready": True,
            "supported_companies": len(self.supported_tickers),
            "supported_filing_types": len(self.filing_types),
            "query_intents": list(self.query_patterns.keys()),
            "companies": self.supported_tickers,
            "filing_types": self.filing_types,
            "llm_service": llm_service.get_service_status(),
            "vectorizer_service": document_vectorizer.get_pipeline_status()
        }


# Global query engine instance
query_engine = QueryEngine()
