#!/usr/bin/env python3
"""
SEC API Testing Script
"""

import sys
import os
import asyncio

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app.services.sec_api import SECAPIClient, CompanyManager, FilingManager


async def test_sec_api_client():
    """Test basic SEC API client functionality"""
    print("\n🔍 Testing SEC API Client...")
    
    try:
        async with SECAPIClient() as client:
            # Test company tickers
            print("  📊 Testing company tickers retrieval...")
            tickers = await client.get_company_tickers()
            print(f"  ✅ Retrieved {len(tickers)} companies")
            
            # Test a specific company (Apple)
            print("  🍎 Testing Apple (AAPL) submissions...")
            # Apple's CIK is 320193
            submissions = await client.get_submissions("320193")
            recent_filings = submissions.get('filings', {}).get('recent', {})
            filing_count = len(recent_filings.get('accessionNumber', []))
            print(f"  ✅ Retrieved {filing_count} recent filings for Apple")
            
            return True
            
    except Exception as e:
        print(f"  ❌ SEC API Client test failed: {str(e)}")
        return False


async def test_company_manager():
    """Test company manager functionality"""
    print("\n🏢 Testing Company Manager...")
    
    try:
        manager = CompanyManager()
        
        # Test ticker to CIK lookup
        print("  🔍 Testing ticker to CIK lookup...")
        cik = await manager.get_cik_by_ticker("AAPL")
        print(f"  ✅ AAPL CIK: {cik}")
        
        # Test company info
        print("  📋 Testing company info retrieval...")
        company_info = await manager.get_company_info("AAPL")
        if company_info:
            print(f"  ✅ Apple Inc.: {company_info.get('title', 'N/A')}")
        
        # Test company search
        print("  🔎 Testing company search...")
        results = await manager.search_companies("Apple", limit=3)
        print(f"  ✅ Found {len(results)} companies matching 'Apple'")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Company Manager test failed: {str(e)}")
        return False


async def test_filing_manager():
    """Test filing manager functionality"""
    print("\n📄 Testing Filing Manager...")
    
    try:
        manager = FilingManager()
        
        # Test getting company filings
        print("  📊 Testing company filings retrieval...")
        filings = await manager.get_company_filings("AAPL", limit=5)
        print(f"  ✅ Retrieved {len(filings)} filings for AAPL")
        
        if filings:
            latest_filing = filings[0]
            print(f"  📋 Latest filing: {latest_filing['form']} ({latest_filing['filing_date']})")
            
            # Test getting filing content (just first few characters)
            print("  📖 Testing filing content retrieval...")
            try:
                content = await manager.get_filing_content(
                    latest_filing['ticker'],
                    latest_filing['accession_number'],
                    latest_filing['primary_document']
                )
                print(f"  ✅ Retrieved filing content ({len(content)} characters)")
                print(f"  📝 Content preview: {content[:200]}...")
            except Exception as e:
                print(f"  ⚠️  Filing content test failed (this is normal): {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Filing Manager test failed: {str(e)}")
        return False


async def test_rate_limiting():
    """Test rate limiting functionality"""
    print("\n⏱️  Testing Rate Limiting...")
    
    try:
        from app.services.sec_api import RateLimiter
        import time
        
        rate_limiter = RateLimiter(requests_per_second=2)  # 2 requests per second
        
        start_time = time.time()
        
        # Make 3 requests
        for i in range(3):
            await rate_limiter.wait_if_needed()
            print(f"  📡 Request {i+1} completed")
        
        elapsed_time = time.time() - start_time
        print(f"  ✅ Rate limiting working: {elapsed_time:.2f} seconds for 3 requests")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Rate limiting test failed: {str(e)}")
        return False


async def main():
    """Main test function"""
    print("🚀 SEC API Integration Tests")
    print("=" * 50)
    
    # Check environment
    from app.core.config import settings
    print(f"📧 SEC API User Agent: {settings.SEC_API_USER_AGENT}")
    print(f"🌐 SEC API Base URL: {settings.SEC_API_BASE_URL}")
    
    if settings.SEC_API_USER_AGENT == "<EMAIL>":
        print("⚠️  WARNING: Please set SEC_API_USER_AGENT to your email in .env file")
        print("   The SEC requires a valid email address in the User-Agent header")
    
    # Run tests
    tests = [
        ("Rate Limiting", test_rate_limiting),
        ("SEC API Client", test_sec_api_client),
        ("Company Manager", test_company_manager),
        ("Filing Manager", test_filing_manager),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All SEC API tests passed!")
        return 0
    else:
        print("💥 Some SEC API tests failed!")
        return 1


if __name__ == "__main__":
    # Check if we're in the right directory
    if not os.path.exists("backend/app"):
        print("❌ Please run this script from the project root directory")
        print("   Example: python scripts/test_sec_api.py")
        sys.exit(1)
    
    # Run the tests
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
