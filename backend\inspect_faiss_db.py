#!/usr/bin/env python3
"""
FAISS Database Inspector
Shows all records present in the FAISS vector index with detailed metadata
"""

import json
import numpy as np
from pathlib import Path
from datetime import datetime
from app.services.vector_storage import vector_storage

def inspect_faiss_database():
    """Inspect and display all records in the FAISS database"""
    print("🗄️  FAISS DATABASE INSPECTOR")
    print("=" * 60)
    print(f"📅 Inspection Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    print()
    
    # Get storage statistics
    print("📊 DATABASE STATISTICS")
    print("-" * 40)
    
    stats = vector_storage.get_storage_stats()
    
    print(f"🔧 Storage Service: {stats['current_service']}")
    print(f"📏 Vector Dimension: {stats['dimension']}")
    print(f"📊 Total Vectors: {stats.get('faiss_vectors_count', 0)}")
    print(f"📋 Metadata Records: {stats.get('faiss_metadata_count', 0)}")
    print(f"💾 Index Path: {stats.get('faiss_index_path', 'N/A')}")
    print(f"📄 Metadata Path: {stats.get('faiss_metadata_path', 'N/A')}")
    print()
    
    # Check if database exists
    if stats.get('faiss_vectors_count', 0) == 0:
        print("⚠️  No vectors found in FAISS database")
        return
    
    # Load and display metadata
    print("📋 DETAILED RECORD INSPECTION")
    print("-" * 40)
    
    try:
        # Access the metadata directly
        metadata = vector_storage.faiss_metadata
        
        if not metadata:
            print("⚠️  No metadata found")
            return
        
        print(f"📊 Found {len(metadata)} records:")
        print()
        
        # Group records by company and filing type
        companies = {}
        filing_types = {}
        sections = {}
        dates = {}
        
        for vector_id, record in metadata.items():
            # Group by company
            ticker = record.get('ticker', 'Unknown')
            if ticker not in companies:
                companies[ticker] = []
            companies[ticker].append(record)
            
            # Group by filing type
            filing_type = record.get('filing_type', 'Unknown')
            filing_types[filing_type] = filing_types.get(filing_type, 0) + 1
            
            # Group by section
            section = record.get('section_name', 'Unknown')
            sections[section] = sections.get(section, 0) + 1
            
            # Group by date
            date = record.get('filing_date', 'Unknown')
            dates[date] = dates.get(date, 0) + 1
        
        # Display summary statistics
        print("📈 SUMMARY STATISTICS")
        print("-" * 30)
        print(f"🏢 Companies: {len(companies)}")
        for ticker, records in companies.items():
            print(f"  - {ticker}: {len(records)} records")
        
        print(f"\n📄 Filing Types: {len(filing_types)}")
        for filing_type, count in filing_types.items():
            print(f"  - {filing_type}: {count} records")
        
        print(f"\n📂 Sections: {len(sections)}")
        for section, count in sections.items():
            print(f"  - {section}: {count} records")
        
        print(f"\n📅 Filing Dates: {len(dates)}")
        for date, count in sorted(dates.items()):
            print(f"  - {date}: {count} records")
        
        print()
        
        # Display detailed records
        print("📋 DETAILED RECORDS")
        print("-" * 30)
        
        for i, (vector_id, record) in enumerate(metadata.items(), 1):
            print(f"📄 Record {i} (Vector ID: {vector_id})")
            print(f"  🏢 Company: {record.get('ticker', 'Unknown')}")
            print(f"  📄 Filing Type: {record.get('filing_type', 'Unknown')}")
            print(f"  📅 Filing Date: {record.get('filing_date', 'Unknown')}")
            print(f"  📂 Section: {record.get('section_name', 'Unknown')}")
            print(f"  🔢 Chunk ID: {record.get('chunk_id', 'Unknown')}")
            print(f"  📏 Chunk Size: {record.get('chunk_size', 'Unknown')} characters")
            print(f"  💰 Has Financial Data: {record.get('has_financial_data', False)}")
            print(f"  📊 Embedding Dimension: {record.get('embedding_dimension', 'Unknown')}")
            print(f"  🔧 Embedding Service: {record.get('embedding_service', 'Unknown')}")
            print(f"  📅 Processed At: {record.get('processed_at', 'Unknown')}")
            
            # Show content preview
            content_preview = record.get('content_preview', '')
            if content_preview:
                preview = content_preview[:150] + "..." if len(content_preview) > 150 else content_preview
                print(f"  📝 Content Preview: {preview}")
            
            # Show key phrases if available
            key_phrases = record.get('key_phrases', [])
            if key_phrases:
                print(f"  🔑 Key Phrases: {', '.join(key_phrases[:5])}")
            
            print()
        
        # Show vector index information
        print("🔍 VECTOR INDEX INFORMATION")
        print("-" * 30)
        
        if hasattr(vector_storage, 'faiss_index') and vector_storage.faiss_index:
            index = vector_storage.faiss_index
            print(f"📊 Index Type: {type(index).__name__}")
            print(f"📏 Vector Dimension: {index.d}")
            print(f"📈 Total Vectors: {index.ntotal}")
            print(f"🔧 Index Trained: {index.is_trained}")
            print(f"💾 Index Size: ~{index.ntotal * index.d * 4 / 1024 / 1024:.2f} MB")
        
        print()
        
        # Show file information
        print("💾 FILE INFORMATION")
        print("-" * 30)
        
        index_path = Path(stats.get('faiss_index_path', ''))
        metadata_path = Path(stats.get('faiss_metadata_path', ''))
        
        if index_path.exists():
            index_size = index_path.stat().st_size
            print(f"📄 Index File: {index_path.name}")
            print(f"  📏 Size: {index_size / 1024:.2f} KB")
            print(f"  📅 Modified: {datetime.fromtimestamp(index_path.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')}")
        
        if metadata_path.exists():
            metadata_size = metadata_path.stat().st_size
            print(f"📄 Metadata File: {metadata_path.name}")
            print(f"  📏 Size: {metadata_size / 1024:.2f} KB")
            print(f"  📅 Modified: {datetime.fromtimestamp(metadata_path.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')}")
        
        print()
        
        # Show sample vector similarity (if multiple vectors exist)
        if len(metadata) > 1:
            print("🔍 SAMPLE VECTOR ANALYSIS")
            print("-" * 30)
            
            try:
                # Get first two vectors for similarity comparison
                vector_ids = list(metadata.keys())[:2]
                
                if hasattr(vector_storage, 'faiss_index') and vector_storage.faiss_index:
                    # Get vectors
                    vectors = []
                    for vid in vector_ids:
                        vector_idx = int(vid) if isinstance(vid, str) and vid.isdigit() else vid
                        if vector_idx < vector_storage.faiss_index.ntotal:
                            # This is a simplified approach - in practice you'd need to reconstruct vectors
                            print(f"  📊 Vector {vector_idx}: Available in index")
                
                print(f"  🔍 Similarity analysis available for {len(vector_ids)} vectors")
                
            except Exception as e:
                print(f"  ⚠️  Vector analysis error: {str(e)}")
        
    except Exception as e:
        print(f"❌ Error inspecting database: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print()
    print("🎉 FAISS DATABASE INSPECTION COMPLETE!")
    print("=" * 60)

if __name__ == "__main__":
    inspect_faiss_database()
