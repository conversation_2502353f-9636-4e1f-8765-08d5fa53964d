"""
Document Chunking Service
Handles intelligent chunking of SEC filing documents for vector storage
"""

import re
import uuid
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import structlog

from app.core.config import settings

logger = structlog.get_logger()


class DocumentChunker:
    """Intelligent document chunking for SEC filings"""
    
    def __init__(self, 
                 chunk_size: int = None, 
                 chunk_overlap: int = None,
                 min_chunk_size: int = 100):
        self.chunk_size = chunk_size or settings.CHUNK_SIZE
        self.chunk_overlap = chunk_overlap or settings.CHUNK_OVERLAP
        self.min_chunk_size = min_chunk_size
        
        # Sentence boundary patterns
        self.sentence_endings = re.compile(r'[.!?]+\s+')
        self.paragraph_breaks = re.compile(r'\n\s*\n')
        
        # Section boundary patterns
        self.section_headers = re.compile(r'(?i)^(item\s+\d+[a-z]?\.?|part\s+[ivx]+\.?)', re.MULTILINE)
    
    def chunk_document(self, parsed_document: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Chunk a parsed document into smaller pieces for vector storage
        
        Args:
            parsed_document: Document parsed by DocumentParser
            
        Returns:
            List of document chunks with metadata
        """
        try:
            logger.info("Chunking document", 
                       ticker=parsed_document['metadata']['ticker'],
                       filing_type=parsed_document['metadata']['filing_type'])
            
            chunks = []
            
            # Chunk each section separately to preserve context
            for section_name, section_text in parsed_document['sections'].items():
                section_chunks = self._chunk_section(
                    section_text=section_text,
                    section_name=section_name,
                    document_metadata=parsed_document['metadata']
                )
                chunks.extend(section_chunks)
            
            # If no sections were found, chunk the full text
            if not chunks and parsed_document['full_text']:
                full_text_chunks = self._chunk_section(
                    section_text=parsed_document['full_text'],
                    section_name='full_document',
                    document_metadata=parsed_document['metadata']
                )
                chunks.extend(full_text_chunks)
            
            # Add chunk sequence numbers
            for i, chunk in enumerate(chunks):
                chunk['chunk_sequence'] = i
                chunk['total_chunks'] = len(chunks)
            
            logger.info("Document chunking completed", 
                       ticker=parsed_document['metadata']['ticker'],
                       chunks_created=len(chunks))
            
            return chunks
            
        except Exception as e:
            logger.error("Document chunking failed", 
                        ticker=parsed_document['metadata'].get('ticker', 'unknown'),
                        error=str(e))
            raise
    
    def _chunk_section(self, section_text: str, section_name: str, 
                      document_metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Chunk a single section of the document"""
        
        if len(section_text) <= self.chunk_size:
            # Section is small enough to be a single chunk
            return [self._create_chunk(
                text=section_text,
                section_name=section_name,
                document_metadata=document_metadata,
                chunk_index=0,
                start_pos=0,
                end_pos=len(section_text)
            )]
        
        # Split section into chunks
        chunks = []
        chunk_index = 0
        
        # Try different chunking strategies in order of preference
        chunk_boundaries = self._find_optimal_boundaries(section_text)
        
        for start_pos, end_pos in chunk_boundaries:
            chunk_text = section_text[start_pos:end_pos].strip()
            
            if len(chunk_text) >= self.min_chunk_size:
                chunk = self._create_chunk(
                    text=chunk_text,
                    section_name=section_name,
                    document_metadata=document_metadata,
                    chunk_index=chunk_index,
                    start_pos=start_pos,
                    end_pos=end_pos
                )
                chunks.append(chunk)
                chunk_index += 1
        
        return chunks
    
    def _find_optimal_boundaries(self, text: str) -> List[Tuple[int, int]]:
        """Find optimal chunk boundaries preserving sentence and paragraph structure"""
        
        boundaries = []
        text_length = len(text)
        
        start_pos = 0
        
        while start_pos < text_length:
            # Calculate ideal end position
            ideal_end = start_pos + self.chunk_size
            
            if ideal_end >= text_length:
                # Last chunk
                boundaries.append((start_pos, text_length))
                break
            
            # Find the best boundary near the ideal end position
            actual_end = self._find_best_boundary(text, start_pos, ideal_end)
            
            boundaries.append((start_pos, actual_end))
            
            # Next chunk starts with overlap
            start_pos = max(start_pos + 1, actual_end - self.chunk_overlap)
        
        return boundaries
    
    def _find_best_boundary(self, text: str, start_pos: int, ideal_end: int) -> int:
        """Find the best boundary near the ideal end position"""
        
        # Search window around ideal end
        search_start = max(start_pos + self.min_chunk_size, ideal_end - 200)
        search_end = min(len(text), ideal_end + 200)
        search_text = text[search_start:search_end]
        
        # Priority 1: Paragraph breaks
        paragraph_matches = list(self.paragraph_breaks.finditer(search_text))
        if paragraph_matches:
            # Find the match closest to ideal position
            best_match = min(paragraph_matches, 
                           key=lambda m: abs((search_start + m.end()) - ideal_end))
            return search_start + best_match.end()
        
        # Priority 2: Sentence endings
        sentence_matches = list(self.sentence_endings.finditer(search_text))
        if sentence_matches:
            # Find the match closest to ideal position
            best_match = min(sentence_matches,
                           key=lambda m: abs((search_start + m.end()) - ideal_end))
            return search_start + best_match.end()
        
        # Priority 3: Word boundaries
        # Look for spaces near the ideal end
        for offset in range(0, 100):
            # Check positions before and after ideal end
            for pos in [ideal_end - offset, ideal_end + offset]:
                if start_pos < pos < len(text) and text[pos].isspace():
                    return pos
        
        # Fallback: Use ideal end position
        return min(ideal_end, len(text))
    
    def _create_chunk(self, text: str, section_name: str, 
                     document_metadata: Dict[str, Any], chunk_index: int,
                     start_pos: int, end_pos: int) -> Dict[str, Any]:
        """Create a chunk with comprehensive metadata"""
        
        chunk_id = str(uuid.uuid4())
        
        # Extract key phrases and entities from the chunk
        key_phrases = self._extract_key_phrases(text)
        
        chunk = {
            'id': chunk_id,
            'text': text,
            'metadata': {
                # Document-level metadata
                'ticker': document_metadata['ticker'],
                'filing_type': document_metadata['filing_type'],
                'filing_date': document_metadata['filing_date'],
                'company_name': document_metadata.get('company_name'),
                'cik': document_metadata.get('cik'),
                'period_end_date': document_metadata.get('period_end_date'),
                
                # Section-level metadata
                'section_name': section_name,
                'section_type': self._classify_section_type(section_name),
                
                # Chunk-level metadata
                'chunk_index': chunk_index,
                'chunk_id': chunk_id,
                'start_position': start_pos,
                'end_position': end_pos,
                'chunk_length': len(text),
                'word_count': len(text.split()),
                
                # Content metadata
                'key_phrases': key_phrases,
                'has_financial_data': self._contains_financial_data(text),
                'has_numbers': bool(re.search(r'\d+', text)),
                'has_percentages': bool(re.search(r'\d+%', text)),
                'has_dates': bool(re.search(r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}', text)),
                
                # Processing metadata
                'created_at': datetime.utcnow().isoformat(),
                'chunk_strategy': 'intelligent_boundary'
            }
        }
        
        return chunk
    
    def _extract_key_phrases(self, text: str) -> List[str]:
        """Extract key phrases from chunk text"""
        key_phrases = []
        
        # Financial terms
        financial_terms = [
            'revenue', 'income', 'profit', 'loss', 'earnings', 'cash flow',
            'assets', 'liabilities', 'equity', 'debt', 'investment',
            'dividend', 'share', 'stock', 'market', 'growth', 'risk'
        ]
        
        text_lower = text.lower()
        for term in financial_terms:
            if term in text_lower:
                key_phrases.append(term)
        
        # Extract capitalized phrases (likely important terms)
        capitalized_phrases = re.findall(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b', text)
        key_phrases.extend(capitalized_phrases[:5])  # Limit to top 5
        
        return list(set(key_phrases))  # Remove duplicates
    
    def _classify_section_type(self, section_name: str) -> str:
        """Classify the type of section"""
        section_types = {
            'financial': ['financial_statements', 'balance_sheet', 'income_statement', 'cash_flow'],
            'risk': ['risk_factors', 'market_risk'],
            'business': ['business', 'operations', 'products', 'services'],
            'legal': ['legal_proceedings', 'litigation', 'regulatory'],
            'governance': ['management_discussion', 'controls_procedures', 'corporate_governance'],
            'compensation': ['executive_compensation', 'director_compensation']
        }
        
        section_lower = section_name.lower()
        for section_type, keywords in section_types.items():
            if any(keyword in section_lower for keyword in keywords):
                return section_type
        
        return 'other'
    
    def _contains_financial_data(self, text: str) -> bool:
        """Check if text contains financial data"""
        financial_indicators = [
            r'\$[\d,]+(?:\.\d{2})?',  # Dollar amounts
            r'\d+(?:\.\d+)?\s*(?:million|billion|thousand)',  # Large numbers
            r'\d+(?:\.\d+)?%',  # Percentages
            r'(?i)revenue|income|profit|loss|earnings|ebitda',  # Financial terms
        ]
        
        for pattern in financial_indicators:
            if re.search(pattern, text):
                return True
        
        return False
    
    def create_chunk_summary(self, chunks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create a summary of the chunking results"""
        if not chunks:
            return {}
        
        # Aggregate statistics
        total_chunks = len(chunks)
        total_words = sum(chunk['metadata']['word_count'] for chunk in chunks)
        avg_chunk_size = sum(chunk['metadata']['chunk_length'] for chunk in chunks) / total_chunks
        
        # Section distribution
        sections = {}
        for chunk in chunks:
            section = chunk['metadata']['section_name']
            sections[section] = sections.get(section, 0) + 1
        
        # Content analysis
        financial_chunks = sum(1 for chunk in chunks if chunk['metadata']['has_financial_data'])
        
        summary = {
            'total_chunks': total_chunks,
            'total_words': total_words,
            'average_chunk_size': int(avg_chunk_size),
            'sections_distribution': sections,
            'financial_chunks': financial_chunks,
            'financial_percentage': (financial_chunks / total_chunks * 100) if total_chunks > 0 else 0,
            'chunking_timestamp': datetime.utcnow().isoformat()
        }
        
        return summary


# Global chunker instance
document_chunker = DocumentChunker()
