@echo off
REM Windows Batch Script to Start SEC Filing QA Agent Servers
REM Starts both FastAPI backend and React frontend

echo.
echo ========================================
echo   SEC Filing QA Agent - Server Startup
echo ========================================
echo.

REM Check if directories exist
if not exist "backend" (
    echo ❌ Backend directory not found!
    echo Please run this script from the project root directory.
    pause
    exit /b 1
)

if not exist "frontend" (
    echo ❌ Frontend directory not found!
    echo Please run this script from the project root directory.
    pause
    exit /b 1
)

echo 🔍 Checking prerequisites...

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python 3.8+
    pause
    exit /b 1
)

REM Check Node.js/npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm not found! Please install Node.js
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed
echo.

REM Install backend dependencies if needed
echo 📦 Checking backend dependencies...
cd backend
if not exist "venv" (
    echo Creating Python virtual environment...
    python -m venv venv
)

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo Installing/updating backend dependencies...
pip install -r requirements.txt >nul 2>&1

cd ..

REM Install frontend dependencies if needed
echo 📦 Checking frontend dependencies...
cd frontend
if not exist "node_modules" (
    echo Installing frontend dependencies...
    npm install
)
cd ..

echo.
echo 🚀 Starting servers...
echo.

REM Start backend server in new window
echo 🔧 Starting Backend Server (FastAPI)...
start "SEC QA Backend" cmd /k "cd backend && venv\Scripts\activate.bat && python -m uvicorn app.main:app --reload --host 127.0.0.1 --port 8000"

REM Wait a moment for backend to start
timeout /t 5 /nobreak >nul

REM Start frontend server in new window
echo 🌐 Starting Frontend Server (React)...
start "SEC QA Frontend" cmd /k "cd frontend && npm run dev"

REM Wait for servers to start
echo ⏳ Waiting for servers to start...
timeout /t 10 /nobreak >nul

echo.
echo 🎉 SERVERS STARTING!
echo ========================================
echo 🌐 Frontend: http://localhost:5173
echo 🔧 Backend:  http://127.0.0.1:8000
echo 📚 API Docs: http://127.0.0.1:8000/docs
echo ========================================
echo.
echo 💡 TESTING INSTRUCTIONS:
echo 1. Wait for both server windows to show "ready" status
echo 2. Open http://localhost:5173 in your browser
echo 3. Try asking: "What are Apple's main revenue sources?"
echo 4. Test advanced features: history, bookmarks, export
echo.
echo 🧪 RUN QUICK TEST:
echo    python quick_test.py
echo.
echo 🧪 RUN FULL INTEGRATION TEST:
echo    python test_integration_complete.py
echo.
echo ⚠️  To stop servers: Close the server windows or press Ctrl+C in each
echo.

pause
