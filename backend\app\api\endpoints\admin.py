"""
Admin and Management Endpoints - Phase 5 Advanced Features
"""

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
from datetime import datetime
import structlog

from app.services.query_cache import query_cache
from app.services.query_history import query_history

logger = structlog.get_logger()
router = APIRouter()


class CacheStats(BaseModel):
    """Cache statistics model"""
    total_entries: int
    total_size_bytes: int
    total_size_mb: float
    hit_rate_percent: float
    stats: Dict[str, int]
    recent_entries: List[Dict[str, Any]]
    cache_dir: str


class HistoryStats(BaseModel):
    """History statistics model"""
    period_days: int
    total_queries: int
    successful_queries: int
    success_rate: float
    average_processing_time: float
    most_used_models: Dict[str, int]
    top_query_words: Dict[str, int]
    daily_query_counts: Dict[str, int]
    unique_users: int
    unique_sessions: int


@router.get("/cache/stats", response_model=CacheStats)
async def get_cache_stats():
    """
    Get query cache statistics
    
    Returns comprehensive cache statistics including hit rates,
    storage usage, and recent cache entries.
    """
    try:
        stats = await query_cache.get_stats()
        
        logger.info("Cache stats retrieved", 
                   total_entries=stats.get("total_entries", 0),
                   hit_rate=stats.get("hit_rate_percent", 0))
        
        return CacheStats(**stats)
        
    except Exception as e:
        logger.error("Failed to get cache stats", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve cache statistics")


@router.post("/cache/clear")
async def clear_cache():
    """
    Clear all cache entries
    
    Removes all cached query results. This action cannot be undone.
    """
    try:
        success = await query_cache.clear_all()
        
        if success:
            logger.info("Cache cleared successfully")
            return {"message": "Cache cleared successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to clear cache")
            
    except Exception as e:
        logger.error("Failed to clear cache", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to clear cache")


@router.post("/cache/cleanup")
async def cleanup_expired_cache():
    """
    Clean up expired cache entries
    
    Removes only expired cache entries, keeping valid ones intact.
    """
    try:
        cleared_count = await query_cache.clear_expired()
        
        logger.info("Expired cache entries cleaned up", count=cleared_count)
        return {
            "message": f"Cleaned up {cleared_count} expired cache entries",
            "cleared_count": cleared_count
        }
        
    except Exception as e:
        logger.error("Failed to cleanup expired cache", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to cleanup expired cache")


@router.delete("/cache/query")
async def invalidate_query_cache(
    query: str = Query(..., description="Query to invalidate"),
    filters: Optional[str] = Query(None, description="JSON string of filters")
):
    """
    Invalidate cache for a specific query
    
    Removes the cached result for a specific query and filter combination.
    """
    try:
        import json
        
        filters_dict = None
        if filters:
            try:
                filters_dict = json.loads(filters)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Invalid filters JSON")
        
        success = await query_cache.invalidate(query, filters_dict)
        
        if success:
            logger.info("Query cache invalidated", query=query[:100])
            return {"message": "Query cache invalidated successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to invalidate query cache")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to invalidate query cache", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to invalidate query cache")


@router.get("/history/stats", response_model=HistoryStats)
async def get_history_stats(
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    user_id: Optional[str] = Query(None, description="Filter by user ID")
):
    """
    Get query history analytics
    
    Returns comprehensive analytics about query history including
    success rates, popular queries, and usage patterns.
    """
    try:
        stats = await query_history.get_analytics(user_id=user_id, days=days)
        
        logger.info("History stats retrieved",
                   period_days=days,
                   total_queries=stats.get("total_queries", 0),
                   user_id=user_id)
        
        return HistoryStats(**stats)
        
    except Exception as e:
        logger.error("Failed to get history stats", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve history statistics")


@router.get("/history")
async def get_query_history(
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    session_id: Optional[str] = Query(None, description="Filter by session ID"),
    limit: int = Query(50, ge=1, le=1000, description="Maximum number of entries"),
    offset: int = Query(0, ge=0, description="Number of entries to skip")
):
    """
    Get query history with filtering and pagination
    
    Returns a list of historical queries with their results and metadata.
    """
    try:
        history = await query_history.get_history(
            user_id=user_id,
            session_id=session_id,
            limit=limit,
            offset=offset
        )
        
        logger.info("Query history retrieved",
                   count=len(history),
                   user_id=user_id,
                   session_id=session_id)
        
        return {
            "history": history,
            "count": len(history),
            "limit": limit,
            "offset": offset,
            "filters": {
                "user_id": user_id,
                "session_id": session_id
            }
        }
        
    except Exception as e:
        logger.error("Failed to get query history", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve query history")


@router.get("/history/{query_id}")
async def get_query_by_id(query_id: str):
    """
    Get specific query by ID
    
    Returns detailed information about a specific query including
    the full request, response, and metadata.
    """
    try:
        query_entry = await query_history.get_query_by_id(query_id)
        
        if not query_entry:
            raise HTTPException(status_code=404, detail="Query not found")
        
        logger.info("Query retrieved by ID", query_id=query_id)
        return query_entry
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get query by ID", query_id=query_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve query")


@router.post("/history/export")
async def export_query_history(
    format: str = Query("json", description="Export format (json, csv)"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)")
):
    """
    Export query history
    
    Creates an export file with query history data in the specified format.
    Returns the file path for download.
    """
    try:
        if format not in ["json", "csv"]:
            raise HTTPException(status_code=400, detail="Unsupported export format")
        
        export_path = await query_history.export_history(
            format=format,
            user_id=user_id,
            start_date=start_date,
            end_date=end_date
        )
        
        if not export_path:
            raise HTTPException(status_code=500, detail="Export failed")
        
        logger.info("Query history exported",
                   format=format,
                   export_path=export_path,
                   user_id=user_id)
        
        return {
            "message": "Export completed successfully",
            "export_path": export_path,
            "format": format,
            "filters": {
                "user_id": user_id,
                "start_date": start_date,
                "end_date": end_date
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to export query history", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to export query history")


@router.get("/system/status")
async def get_system_status():
    """
    Get comprehensive system status
    
    Returns detailed information about all system components,
    performance metrics, and service health.
    """
    try:
        # Get cache stats
        cache_stats = await query_cache.get_stats()
        
        # Get history stats
        history_stats = await query_history.get_analytics(days=7)
        
        # Get query engine status
        from app.services.query_engine import query_engine
        engine_status = query_engine.get_engine_status()
        
        # Get vector storage stats
        from app.services.vector_storage import vector_storage
        vector_stats = vector_storage.get_storage_stats()
        
        # Get LLM service status
        from app.services.llm_service import llm_service
        llm_status = llm_service.get_service_status()
        
        system_status = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy",
            "components": {
                "cache": {
                    "status": "healthy",
                    "entries": cache_stats.get("total_entries", 0),
                    "hit_rate": cache_stats.get("hit_rate_percent", 0),
                    "size_mb": cache_stats.get("total_size_mb", 0)
                },
                "history": {
                    "status": "healthy",
                    "total_queries": history_stats.get("total_queries", 0),
                    "success_rate": history_stats.get("success_rate", 0),
                    "unique_users": history_stats.get("unique_users", 0)
                },
                "query_engine": {
                    "status": "healthy" if engine_status.get("supported_companies", 0) > 0 else "degraded",
                    "supported_companies": engine_status.get("supported_companies", 0),
                    "query_intents": len(engine_status.get("query_intents", []))
                },
                "vector_storage": {
                    "status": "healthy" if vector_stats.get("faiss_vectors_count", 0) > 0 else "degraded",
                    "vectors": vector_stats.get("faiss_vectors_count", 0),
                    "dimension": vector_stats.get("dimension", 0)
                },
                "llm_service": {
                    "status": "healthy" if llm_status.get("api_key_configured", False) else "degraded",
                    "primary_model": llm_status.get("primary_model", "unknown"),
                    "total_requests": llm_status.get("total_requests", 0),
                    "total_errors": llm_status.get("total_errors", 0)
                }
            },
            "performance": {
                "average_query_time": history_stats.get("average_processing_time", 0),
                "cache_hit_rate": cache_stats.get("hit_rate_percent", 0),
                "success_rate": history_stats.get("success_rate", 0)
            }
        }
        
        logger.info("System status retrieved")
        return system_status
        
    except Exception as e:
        logger.error("Failed to get system status", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve system status")


@router.post("/system/maintenance")
async def run_maintenance():
    """
    Run system maintenance tasks
    
    Performs cleanup operations including expired cache removal,
    old history cleanup, and system optimization.
    """
    try:
        maintenance_results = {}
        
        # Clean expired cache
        try:
            cleared_cache = await query_cache.clear_expired()
            maintenance_results["cache_cleanup"] = {
                "status": "success",
                "cleared_entries": cleared_cache
            }
        except Exception as e:
            maintenance_results["cache_cleanup"] = {
                "status": "failed",
                "error": str(e)
            }
        
        # Additional maintenance tasks can be added here
        
        logger.info("System maintenance completed", results=maintenance_results)
        
        return {
            "message": "System maintenance completed",
            "timestamp": datetime.now().isoformat(),
            "results": maintenance_results
        }
        
    except Exception as e:
        logger.error("System maintenance failed", error=str(e))
        raise HTTPException(status_code=500, detail="System maintenance failed")
