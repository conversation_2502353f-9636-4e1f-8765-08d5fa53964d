"""
Vector Database Service
Handles connections to Pinecone and FAISS vector databases
"""

import os
import pickle
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
import structlog

from app.core.config import settings

logger = structlog.get_logger()


class VectorDBInterface(ABC):
    """Abstract interface for vector database operations"""
    
    @abstractmethod
    async def connect(self) -> bool:
        """Connect to the vector database"""
        pass
    
    @abstractmethod
    async def create_index(self, dimension: int, metric: str = "cosine") -> bool:
        """Create a new index"""
        pass
    
    @abstractmethod
    async def upsert_vectors(self, vectors: List[Tuple[str, List[float], Dict[str, Any]]]) -> bool:
        """Insert or update vectors with metadata"""
        pass
    
    @abstractmethod
    async def query_vectors(self, query_vector: List[float], top_k: int = 10, 
                          filter_dict: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Query similar vectors"""
        pass
    
    @abstractmethod
    async def delete_vectors(self, ids: List[str]) -> bool:
        """Delete vectors by IDs"""
        pass
    
    @abstractmethod
    async def get_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        pass


class PineconeVectorDB(VectorDBInterface):
    """Pinecone vector database implementation"""
    
    def __init__(self):
        self.client = None
        self.index = None
        self.index_name = settings.PINECONE_INDEX_NAME
        
    async def connect(self) -> bool:
        """Connect to Pinecone"""
        try:
            import pinecone
            
            # Initialize Pinecone
            pinecone.init(
                api_key=settings.PINECONE_API_KEY,
                environment=settings.PINECONE_ENVIRONMENT
            )
            
            self.client = pinecone
            
            # Check if index exists, if not create it
            if self.index_name not in pinecone.list_indexes():
                logger.info("Creating Pinecone index", index_name=self.index_name)
                await self.create_index(settings.VECTOR_DIMENSION)
            
            # Connect to index
            self.index = pinecone.Index(self.index_name)
            
            logger.info("Connected to Pinecone successfully", index_name=self.index_name)
            return True
            
        except Exception as e:
            logger.error("Failed to connect to Pinecone", error=str(e))
            return False
    
    async def create_index(self, dimension: int, metric: str = "cosine") -> bool:
        """Create a new Pinecone index"""
        try:
            self.client.create_index(
                name=self.index_name,
                dimension=dimension,
                metric=metric
            )
            logger.info("Created Pinecone index", index_name=self.index_name, dimension=dimension)
            return True
        except Exception as e:
            logger.error("Failed to create Pinecone index", error=str(e))
            return False
    
    async def upsert_vectors(self, vectors: List[Tuple[str, List[float], Dict[str, Any]]]) -> bool:
        """Insert or update vectors in Pinecone"""
        try:
            # Convert to Pinecone format
            upsert_data = [
                (vector_id, embedding, metadata)
                for vector_id, embedding, metadata in vectors
            ]
            
            self.index.upsert(vectors=upsert_data)
            logger.info("Upserted vectors to Pinecone", count=len(vectors))
            return True
        except Exception as e:
            logger.error("Failed to upsert vectors to Pinecone", error=str(e))
            return False
    
    async def query_vectors(self, query_vector: List[float], top_k: int = 10, 
                          filter_dict: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Query similar vectors from Pinecone"""
        try:
            response = self.index.query(
                vector=query_vector,
                top_k=top_k,
                filter=filter_dict,
                include_metadata=True,
                include_values=False
            )
            
            results = []
            for match in response.matches:
                results.append({
                    "id": match.id,
                    "score": match.score,
                    "metadata": match.metadata
                })
            
            logger.info("Queried vectors from Pinecone", results_count=len(results))
            return results
        except Exception as e:
            logger.error("Failed to query vectors from Pinecone", error=str(e))
            return []
    
    async def delete_vectors(self, ids: List[str]) -> bool:
        """Delete vectors from Pinecone"""
        try:
            self.index.delete(ids=ids)
            logger.info("Deleted vectors from Pinecone", count=len(ids))
            return True
        except Exception as e:
            logger.error("Failed to delete vectors from Pinecone", error=str(e))
            return False
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get Pinecone index statistics"""
        try:
            stats = self.index.describe_index_stats()
            return {
                "total_vector_count": stats.total_vector_count,
                "dimension": stats.dimension,
                "index_fullness": stats.index_fullness,
                "namespaces": stats.namespaces
            }
        except Exception as e:
            logger.error("Failed to get Pinecone stats", error=str(e))
            return {}


class FAISSVectorDB(VectorDBInterface):
    """FAISS vector database implementation (local alternative)"""
    
    def __init__(self):
        self.index = None
        self.metadata_store = {}
        self.id_to_index = {}
        self.index_to_id = {}
        self.dimension = settings.VECTOR_DIMENSION
        self.index_file = "data/faiss_index.bin"
        self.metadata_file = "data/faiss_metadata.pkl"
        
    async def connect(self) -> bool:
        """Connect to FAISS (load existing index or create new)"""
        try:
            import faiss
            
            # Create data directory if it doesn't exist
            os.makedirs("data", exist_ok=True)
            
            # Try to load existing index
            if os.path.exists(self.index_file) and os.path.exists(self.metadata_file):
                self.index = faiss.read_index(self.index_file)
                with open(self.metadata_file, 'rb') as f:
                    data = pickle.load(f)
                    self.metadata_store = data['metadata']
                    self.id_to_index = data['id_to_index']
                    self.index_to_id = data['index_to_id']
                logger.info("Loaded existing FAISS index", vectors_count=self.index.ntotal)
            else:
                # Create new index
                await self.create_index(self.dimension)
                logger.info("Created new FAISS index")
            
            return True
        except Exception as e:
            logger.error("Failed to connect to FAISS", error=str(e))
            return False
    
    async def create_index(self, dimension: int, metric: str = "cosine") -> bool:
        """Create a new FAISS index"""
        try:
            import faiss
            
            # Create index based on metric
            if metric == "cosine":
                # Normalize vectors for cosine similarity
                self.index = faiss.IndexFlatIP(dimension)
            else:
                # L2 distance
                self.index = faiss.IndexFlatL2(dimension)
            
            self.dimension = dimension
            self.metadata_store = {}
            self.id_to_index = {}
            self.index_to_id = {}
            
            logger.info("Created FAISS index", dimension=dimension, metric=metric)
            return True
        except Exception as e:
            logger.error("Failed to create FAISS index", error=str(e))
            return False
    
    async def upsert_vectors(self, vectors: List[Tuple[str, List[float], Dict[str, Any]]]) -> bool:
        """Insert or update vectors in FAISS"""
        try:
            import faiss
            
            for vector_id, embedding, metadata in vectors:
                # Convert to numpy array and normalize for cosine similarity
                vector = np.array(embedding, dtype=np.float32).reshape(1, -1)
                faiss.normalize_L2(vector)
                
                # Check if vector already exists
                if vector_id in self.id_to_index:
                    # Update existing vector (FAISS doesn't support direct update, so we skip for now)
                    logger.warning("Vector update not supported in FAISS, skipping", vector_id=vector_id)
                    continue
                
                # Add new vector
                current_index = self.index.ntotal
                self.index.add(vector)
                
                # Store metadata and mappings
                self.metadata_store[vector_id] = metadata
                self.id_to_index[vector_id] = current_index
                self.index_to_id[current_index] = vector_id
            
            # Save to disk
            await self._save_index()
            
            logger.info("Upserted vectors to FAISS", count=len(vectors))
            return True
        except Exception as e:
            logger.error("Failed to upsert vectors to FAISS", error=str(e))
            return False
    
    async def query_vectors(self, query_vector: List[float], top_k: int = 10, 
                          filter_dict: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Query similar vectors from FAISS"""
        try:
            import faiss
            
            # Convert query vector and normalize
            query = np.array(query_vector, dtype=np.float32).reshape(1, -1)
            faiss.normalize_L2(query)
            
            # Search
            scores, indices = self.index.search(query, top_k)
            
            results = []
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx == -1:  # No more results
                    break
                
                vector_id = self.index_to_id.get(idx)
                if vector_id is None:
                    continue
                
                metadata = self.metadata_store.get(vector_id, {})
                
                # Apply filters if provided
                if filter_dict:
                    if not self._matches_filter(metadata, filter_dict):
                        continue
                
                results.append({
                    "id": vector_id,
                    "score": float(score),
                    "metadata": metadata
                })
            
            logger.info("Queried vectors from FAISS", results_count=len(results))
            return results
        except Exception as e:
            logger.error("Failed to query vectors from FAISS", error=str(e))
            return []
    
    async def delete_vectors(self, ids: List[str]) -> bool:
        """Delete vectors from FAISS (not directly supported, marked as deleted)"""
        try:
            for vector_id in ids:
                if vector_id in self.metadata_store:
                    # Mark as deleted in metadata
                    self.metadata_store[vector_id]["_deleted"] = True
            
            await self._save_index()
            logger.info("Marked vectors as deleted in FAISS", count=len(ids))
            return True
        except Exception as e:
            logger.error("Failed to delete vectors from FAISS", error=str(e))
            return False
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get FAISS index statistics"""
        try:
            active_vectors = sum(1 for metadata in self.metadata_store.values() 
                               if not metadata.get("_deleted", False))
            
            return {
                "total_vector_count": self.index.ntotal if self.index else 0,
                "active_vector_count": active_vectors,
                "dimension": self.dimension,
                "index_type": "FAISS"
            }
        except Exception as e:
            logger.error("Failed to get FAISS stats", error=str(e))
            return {}
    
    async def _save_index(self):
        """Save FAISS index and metadata to disk"""
        try:
            import faiss
            
            faiss.write_index(self.index, self.index_file)
            
            with open(self.metadata_file, 'wb') as f:
                pickle.dump({
                    'metadata': self.metadata_store,
                    'id_to_index': self.id_to_index,
                    'index_to_id': self.index_to_id
                }, f)
        except Exception as e:
            logger.error("Failed to save FAISS index", error=str(e))
    
    def _matches_filter(self, metadata: Dict[str, Any], filter_dict: Dict[str, Any]) -> bool:
        """Check if metadata matches filter criteria"""
        for key, value in filter_dict.items():
            if key not in metadata or metadata[key] != value:
                return False
        return True


class VectorDBManager:
    """Manager class to handle vector database operations"""
    
    def __init__(self):
        self.db: Optional[VectorDBInterface] = None
        self.db_type = None
    
    async def initialize(self, use_pinecone: bool = True) -> bool:
        """Initialize vector database connection"""
        try:
            if use_pinecone and settings.PINECONE_API_KEY != "placeholder-pinecone-key":
                self.db = PineconeVectorDB()
                self.db_type = "pinecone"
                logger.info("Initializing Pinecone vector database")
            else:
                self.db = FAISSVectorDB()
                self.db_type = "faiss"
                logger.info("Initializing FAISS vector database")
            
            success = await self.db.connect()
            if success:
                logger.info("Vector database initialized successfully", db_type=self.db_type)
            else:
                logger.error("Failed to initialize vector database", db_type=self.db_type)
            
            return success
        except Exception as e:
            logger.error("Error initializing vector database", error=str(e))
            return False
    
    async def health_check(self) -> Dict[str, Any]:
        """Check vector database health"""
        if not self.db:
            return {"status": "not_initialized", "db_type": None}
        
        try:
            stats = await self.db.get_stats()
            return {
                "status": "healthy",
                "db_type": self.db_type,
                "stats": stats
            }
        except Exception as e:
            return {
                "status": "error",
                "db_type": self.db_type,
                "error": str(e)
            }
    
    async def store_document_chunks(self, chunks: List[Dict[str, Any]]) -> bool:
        """Store document chunks with embeddings"""
        if not self.db:
            logger.error("Vector database not initialized")
            return False
        
        # Convert chunks to vector format
        vectors = []
        for chunk in chunks:
            vector_id = chunk.get("id")
            embedding = chunk.get("embedding")
            metadata = {k: v for k, v in chunk.items() if k not in ["id", "embedding"]}
            
            if vector_id and embedding:
                vectors.append((vector_id, embedding, metadata))
        
        return await self.db.upsert_vectors(vectors)
    
    async def search_similar_chunks(self, query_embedding: List[float], 
                                  top_k: int = 10, 
                                  filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Search for similar document chunks"""
        if not self.db:
            logger.error("Vector database not initialized")
            return []
        
        return await self.db.query_vectors(query_embedding, top_k, filters)


# Global vector database manager instance
vector_db_manager = VectorDBManager()
