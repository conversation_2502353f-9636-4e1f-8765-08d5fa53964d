# Core FastAPI dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0
python-multipart==0.0.6

# Data processing
pandas==2.1.3
numpy==1.25.2
beautifulsoup4==4.12.2
requests==2.31.0
python-dateutil==2.8.2
lxml==4.9.3

# Vector databases
pinecone-client==2.2.4
faiss-cpu==1.7.4

# LLM integration
openai==1.3.5
tiktoken==0.5.1
httpx==0.25.2

# Embeddings
sentence-transformers==2.7.0
transformers==4.36.0
huggingface-hub==0.19.4

# Caching and storage
redis==5.0.1
sqlalchemy==2.0.23

# Environment management
python-dotenv==1.0.0

# Logging and monitoring
structlog==23.2.0

# HTTP client
httpx==0.25.2
aiohttp==3.9.1

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0

# Development tools
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
