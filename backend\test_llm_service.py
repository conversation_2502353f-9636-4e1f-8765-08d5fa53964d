#!/usr/bin/env python3
"""
Test script for the LLM Service
Tests OpenRouter API integration with free tier models
"""

import asyncio
from app.services.llm_service import llm_service

async def test_llm_service():
    """Test the LLM service with various scenarios"""
    print("🤖 LLM SERVICE TESTS")
    print("=" * 50)
    
    # Sample context chunks (simulating retrieved SEC filing content)
    sample_context = [
        {
            "score": 0.95,
            "metadata": {
                "ticker": "AAPL",
                "filing_type": "10-Q",
                "filing_date": "2023-07-01",
                "section_name": "financial_statements",
                "accession_number": "**********-23-000064",
                "has_financial_data": True,
                "content_preview": "Apple Inc. reported net sales of $81.8 billion for the third quarter of fiscal 2023, compared to $83.0 billion in the prior year quarter. Product revenue was $60.6 billion and Services revenue was $21.2 billion. iPhone revenue decreased 2% year-over-year to $39.7 billion. Mac revenue decreased 7% to $6.8 billion. iPad revenue decreased 20% to $5.8 billion. Wearables, Home and Accessories revenue decreased 2% to $8.3 billion."
            }
        },
        {
            "score": 0.87,
            "metadata": {
                "ticker": "AAPL", 
                "filing_type": "10-Q",
                "filing_date": "2023-07-01",
                "section_name": "management_discussion",
                "accession_number": "**********-23-000064",
                "has_financial_data": True,
                "content_preview": "The Company's total net sales decreased 1% or $1.4 billion during the third quarter of fiscal 2023 compared to the same quarter in the prior year. This decrease was primarily driven by lower sales of iPhone, Mac, iPad and Wearables, Home and Accessories, partially offset by higher Services net sales. Foreign exchange rates had a negative year-over-year impact on most of the Company's geographic segments."
            }
        },
        {
            "score": 0.82,
            "metadata": {
                "ticker": "AAPL",
                "filing_type": "10-Q", 
                "filing_date": "2023-07-01",
                "section_name": "risk_factors",
                "accession_number": "**********-23-000064",
                "has_financial_data": False,
                "content_preview": "The Company's business can be impacted by political events, international trade disputes, war, terrorism, natural disasters, public health issues, and other business interruptions. The Company's operations and performance depend significantly on global and regional economic conditions. These conditions can affect demand for the Company's products and services."
            }
        }
    ]
    
    print(f"📝 Test context: {len(sample_context)} chunks from Apple 10-Q filing")
    print()
    
    # Test 1: Service Status
    print("🔍 Test 1: Service Status")
    print("-" * 30)
    
    status = llm_service.get_service_status()
    print(f"  Service: {status['service']}")
    print(f"  API Key Configured: {status['api_key_configured']}")
    print(f"  Primary Model: {status['primary_model']}")
    print(f"  Fallback Model: {status['fallback_model']}")
    print(f"  Available Models: {status['available_models']}")
    print(f"  Total Requests: {status['total_requests']}")
    print(f"  Total Errors: {status['total_errors']}")
    
    if not status['api_key_configured']:
        print("  ⚠️  OpenRouter API key not configured - tests will fail")
    
    print()
    
    # Test 2: Available Models
    print("🔍 Test 2: Available Models")
    print("-" * 30)
    
    try:
        models = await llm_service.get_available_models()
        print(f"  ✅ Found {len(models)} available models:")
        
        for model in models:
            print(f"    🤖 {model['key']}: {model['name']}")
            print(f"       Description: {model['description']}")
            print(f"       Max Tokens: {model['max_tokens']}")
            print(f"       Context Window: {model['context_window']}")
            print()
            
    except Exception as e:
        print(f"  ❌ Error getting models: {str(e)}")
    
    print()
    
    # Test 3: Basic Question Answering
    print("🔍 Test 3: Basic Question Answering")
    print("-" * 30)
    
    test_questions = [
        "What was Apple's total revenue in Q3 2023?",
        "How did iPhone sales perform compared to the previous year?",
        "What are the main risk factors mentioned for Apple?"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"  Question {i}: '{question}'")
        
        try:
            result = await llm_service.generate_answer(
                query=question,
                context_chunks=sample_context
            )
            
            print(f"    ✅ Answer generated successfully!")
            print(f"    🤖 Model used: {result['model_used']}")
            print(f"    ⏱️  Response time: {result['response_time_seconds']:.2f} seconds")
            print(f"    📊 Context chunks: {result['context_chunks_used']}")
            print(f"    📝 Answer length: {len(result['answer'])} characters")
            print(f"    🔗 Sources: {len(result['sources'])}")
            
            # Show first 200 characters of answer
            answer_preview = result['answer'][:200] + "..." if len(result['answer']) > 200 else result['answer']
            print(f"    💬 Answer preview: {answer_preview}")
            
            # Show sources
            print(f"    📚 Sources used:")
            for source in result['sources']:
                print(f"      - [{source['ticker']} {source['filing_type']}] {source['section_name']} (Score: {source['score']:.3f})")
            
        except Exception as e:
            print(f"    ❌ Error: {str(e)}")
        
        print()
    
    # Test 4: Model Preference Testing
    print("🔍 Test 4: Model Preference Testing")
    print("-" * 30)
    
    test_question = "Summarize Apple's Q3 2023 financial performance in 2-3 sentences."
    
    for model_key in ["gemma", "deepseek"]:
        print(f"  Testing with {model_key} model:")
        
        try:
            result = await llm_service.generate_answer(
                query=test_question,
                context_chunks=sample_context[:2],  # Use fewer chunks for faster testing
                model_preference=model_key
            )
            
            print(f"    ✅ {model_key} model response:")
            print(f"    🤖 Model: {result['model_used']}")
            print(f"    ⏱️  Time: {result['response_time_seconds']:.2f}s")
            print(f"    📝 Length: {len(result['answer'])} chars")
            
            # Show answer
            answer_preview = result['answer'][:150] + "..." if len(result['answer']) > 150 else result['answer']
            print(f"    💬 Answer: {answer_preview}")
            
        except Exception as e:
            print(f"    ❌ {model_key} model failed: {str(e)}")
        
        print()
    
    # Test 5: Edge Cases
    print("🔍 Test 5: Edge Cases")
    print("-" * 30)
    
    # Test with empty context
    try:
        print("  Testing with empty context:")
        result = await llm_service.generate_answer(
            query="What was Apple's revenue?",
            context_chunks=[]
        )
        print(f"    ✅ Handled empty context gracefully")
        print(f"    💬 Response: {result['answer'][:100]}...")
        
    except Exception as e:
        print(f"    ❌ Empty context test failed: {str(e)}")
    
    print()
    
    # Test with very long question
    try:
        print("  Testing with long question:")
        long_question = "What was Apple's revenue " * 50  # Very long question
        result = await llm_service.generate_answer(
            query=long_question,
            context_chunks=sample_context[:1]
        )
        print(f"    ✅ Handled long question gracefully")
        print(f"    💬 Response length: {len(result['answer'])} chars")
        
    except Exception as e:
        print(f"    ❌ Long question test failed: {str(e)}")
    
    print()
    
    # Test 6: Final Service Status
    print("🔍 Test 6: Final Service Status")
    print("-" * 30)
    
    final_status = llm_service.get_service_status()
    print(f"  Total Requests Made: {final_status['total_requests']}")
    print(f"  Total Errors: {final_status['total_errors']}")
    if final_status['last_error']:
        print(f"  Last Error: {final_status['last_error']}")
    
    print()
    
    # Summary
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    if final_status['api_key_configured']:
        if final_status['total_errors'] == 0:
            print("  🟢 All LLM service tests passed!")
        else:
            print(f"  🟡 LLM service working with {final_status['total_errors']} errors")
        
        print(f"  📊 Total Requests: {final_status['total_requests']}")
        print(f"  🤖 Primary Model: {final_status['primary_model']}")
        print(f"  🔄 Fallback Model: {final_status['fallback_model']}")
    else:
        print("  🟡 LLM service configured but API key missing")
        print("  ℹ️  Add OPENROUTER_API_KEY to environment for full testing")
    
    print(f"  🔧 Service Ready: ✅")
    print()
    print("🎉 All LLM service tests completed!")
    
    # Clean up
    await llm_service.close()

if __name__ == "__main__":
    asyncio.run(test_llm_service())
