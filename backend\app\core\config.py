"""
Application configuration settings
"""

from typing import List
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # API Configuration
    API_HOST: str = Field(default="0.0.0.0", description="API host")
    API_PORT: int = Field(default=8000, description="API port")
    API_RELOAD: bool = Field(default=True, description="API reload in development")
    
    # Environment
    ENVIRONMENT: str = Field(default="development", description="Environment name")
    
    # OpenAI Configuration
    OPENAI_API_KEY: str = Field(default="", description="OpenAI API key")

    # OpenRouter Configuration
    OPENROUTER_API_KEY: str = Field(default="", description="OpenRouter API key")
    OPENROUTER_BASE_URL: str = Field(default="https://openrouter.ai/api/v1", description="OpenRouter API base URL")
    
    # Pinecone Configuration
    PINECONE_API_KEY: str = Field(description="Pinecone API key")
    PINECONE_ENVIRONMENT: str = Field(description="Pinecone environment")
    PINECONE_INDEX_NAME: str = Field(default="sec-filings-qa", description="Pinecone index name")
    
    # SEC API Configuration
    SEC_API_USER_AGENT: str = Field(description="SEC API User-Agent (email)")
    SEC_API_BASE_URL: str = Field(default="https://data.sec.gov", description="SEC API base URL")
    
    # Alternative SEC API (sec-api.io)
    SEC_API_IO_KEY: str = Field(default="", description="sec-api.io API key")
    
    # Redis Configuration
    REDIS_URL: str = Field(default="redis://localhost:6379/0", description="Redis URL")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    
    # CORS Settings
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:5173"],
        description="Allowed CORS origins"
    )
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS_PER_MINUTE: int = Field(default=60, description="Rate limit per minute")
    
    # Vector Database Settings
    VECTOR_DIMENSION: int = Field(default=1536, description="Vector dimension for embeddings")
    CHUNK_SIZE: int = Field(default=1000, description="Document chunk size")
    CHUNK_OVERLAP: int = Field(default=200, description="Document chunk overlap")
    
    # LLM Settings
    MAX_TOKENS: int = Field(default=4000, description="Maximum tokens for LLM responses")
    TEMPERATURE: float = Field(default=0.1, description="LLM temperature")
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()
