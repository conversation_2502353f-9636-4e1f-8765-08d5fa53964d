"""
Hybrid Retrieval System
Combines semantic search with keyword-based search for comprehensive retrieval
"""

import re
from typing import List, Dict, Any, Optional, Set
import structlog
from collections import Counter
import math

from app.services.vector_storage import vector_storage

logger = structlog.get_logger()


class HybridRetrievalSystem:
    """
    Hybrid retrieval system combining semantic and keyword-based search
    """
    
    def __init__(self):
        # Common financial terms for keyword matching
        self.financial_keywords = {
            "revenue": ["revenue", "sales", "net sales", "total revenue"],
            "profit": ["profit", "net income", "earnings", "net earnings"],
            "growth": ["growth", "increase", "decrease", "change", "improvement"],
            "margin": ["margin", "gross margin", "operating margin", "profit margin"],
            "cash": ["cash", "cash flow", "operating cash flow", "free cash flow"],
            "debt": ["debt", "liabilities", "borrowings", "credit"],
            "assets": ["assets", "total assets", "current assets"],
            "equity": ["equity", "shareholders equity", "stockholders equity"]
        }
        
        # Risk-related keywords
        self.risk_keywords = {
            "risk": ["risk", "risks", "risk factors", "uncertainties"],
            "competition": ["competition", "competitive", "competitors"],
            "regulation": ["regulation", "regulatory", "compliance", "legal"],
            "market": ["market conditions", "economic conditions", "market risk"],
            "operational": ["operational risk", "supply chain", "disruption"]
        }
        
        # Business operation keywords
        self.business_keywords = {
            "strategy": ["strategy", "strategic", "business strategy"],
            "products": ["products", "services", "offerings"],
            "customers": ["customers", "clients", "consumer"],
            "operations": ["operations", "operational", "business operations"],
            "technology": ["technology", "innovation", "digital", "AI"]
        }
        
        # All keywords combined
        self.all_keywords = {**self.financial_keywords, **self.risk_keywords, **self.business_keywords}
    
    async def hybrid_search(self, query: str, query_analysis: Dict[str, Any], 
                           max_results: int = 10, semantic_weight: float = 0.7) -> List[Dict[str, Any]]:
        """
        Perform hybrid search combining semantic and keyword-based retrieval
        
        Args:
            query: Search query
            query_analysis: Parsed query information
            max_results: Maximum number of results
            semantic_weight: Weight for semantic search (0-1, remainder goes to keyword)
            
        Returns:
            Combined and ranked search results
        """
        logger.info("Performing hybrid search", 
                   query_length=len(query),
                   max_results=max_results,
                   semantic_weight=semantic_weight)
        
        try:
            # Get semantic search results
            semantic_results = await self._semantic_search(query, query_analysis, max_results * 2)
            
            # Get keyword search results
            keyword_results = await self._keyword_search(query, query_analysis, max_results * 2)
            
            # Combine and rank results
            combined_results = self._combine_results(
                semantic_results, keyword_results, 
                semantic_weight, 1 - semantic_weight
            )
            
            # Apply final ranking
            final_results = self._final_ranking(combined_results, query_analysis)
            
            # Return top results
            return final_results[:max_results]
            
        except Exception as e:
            logger.error("Hybrid search failed", error=str(e))
            # Fallback to semantic search only
            return await self._semantic_search(query, query_analysis, max_results)
    
    async def _semantic_search(self, query: str, query_analysis: Dict[str, Any], 
                              max_results: int) -> List[Dict[str, Any]]:
        """Perform semantic vector search"""
        try:
            # Build metadata filters
            filters = self._build_metadata_filters(query_analysis)
            
            # Use the existing vector storage search
            from app.services.document_vectorizer import document_vectorizer
            
            results = await document_vectorizer.search_similar_content(
                query_text=query,
                top_k=max_results,
                filters=filters
            )
            
            # Add search type metadata
            for result in results:
                result["search_type"] = "semantic"
                result["semantic_score"] = result.get("score", 0.0)
            
            return results
            
        except Exception as e:
            logger.error("Semantic search failed", error=str(e))
            return []
    
    async def _keyword_search(self, query: str, query_analysis: Dict[str, Any], 
                             max_results: int) -> List[Dict[str, Any]]:
        """Perform keyword-based search"""
        try:
            # Extract keywords from query
            query_keywords = self._extract_query_keywords(query)
            
            # Get all stored documents
            all_documents = self._get_all_documents()
            
            # Score documents based on keyword matching
            scored_documents = []
            
            for doc_id, doc_metadata in all_documents.items():
                keyword_score = self._calculate_keyword_score(
                    query_keywords, doc_metadata, query_analysis
                )
                
                if keyword_score > 0:
                    result = {
                        "id": f"keyword_{doc_id}",
                        "score": keyword_score,
                        "metadata": doc_metadata,
                        "search_type": "keyword",
                        "keyword_score": keyword_score,
                        "matched_keywords": self._get_matched_keywords(query_keywords, doc_metadata)
                    }
                    scored_documents.append(result)
            
            # Sort by keyword score
            scored_documents.sort(key=lambda x: x["keyword_score"], reverse=True)
            
            return scored_documents[:max_results]
            
        except Exception as e:
            logger.error("Keyword search failed", error=str(e))
            return []
    
    def _extract_query_keywords(self, query: str) -> List[str]:
        """Extract relevant keywords from query"""
        query_lower = query.lower()
        keywords = []
        
        # Extract words (remove common stop words)
        stop_words = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "is", "are", "was", "were", "be", "been", "have", "has", "had", "do", "does", "did", "will", "would", "could", "should", "may", "might", "can", "what", "how", "when", "where", "why", "who"}
        
        words = re.findall(r'\b\w+\b', query_lower)
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        
        # Add financial term mappings
        for category, terms in self.all_keywords.items():
            for term in terms:
                if term.lower() in query_lower:
                    keywords.extend(term.split())
        
        return list(set(keywords))  # Remove duplicates
    
    def _get_all_documents(self) -> Dict[int, Dict[str, Any]]:
        """Get all documents from vector storage"""
        try:
            return vector_storage.faiss_metadata
        except Exception as e:
            logger.error("Failed to get documents", error=str(e))
            return {}
    
    def _calculate_keyword_score(self, query_keywords: List[str], doc_metadata: Dict[str, Any], 
                                query_analysis: Dict[str, Any]) -> float:
        """Calculate keyword matching score for a document"""
        score = 0.0
        
        # Get document text fields
        content = doc_metadata.get("content_preview", "").lower()
        section_name = doc_metadata.get("section_name", "").lower()
        key_phrases = [phrase.lower() for phrase in doc_metadata.get("key_phrases", [])]
        
        # Combine all searchable text
        searchable_text = f"{content} {section_name} {' '.join(key_phrases)}"
        
        # Calculate TF-IDF-like scoring
        total_words = len(searchable_text.split())
        
        for keyword in query_keywords:
            # Count occurrences
            keyword_count = len(re.findall(r'\b' + re.escape(keyword) + r'\b', searchable_text))
            
            if keyword_count > 0:
                # Term frequency
                tf = keyword_count / max(total_words, 1)
                
                # Boost score for exact matches
                if keyword in searchable_text:
                    score += tf * 2.0
                
                # Boost for financial keywords
                if any(keyword in terms for terms in self.financial_keywords.values()):
                    score += tf * 1.5
                
                # Boost for section name matches
                if keyword in section_name:
                    score += tf * 1.3
                
                # Boost for key phrase matches
                if any(keyword in phrase for phrase in key_phrases):
                    score += tf * 1.2
        
        # Apply metadata boosts
        score = self._apply_metadata_boosts(score, doc_metadata, query_analysis)
        
        return score
    
    def _apply_metadata_boosts(self, base_score: float, doc_metadata: Dict[str, Any], 
                              query_analysis: Dict[str, Any]) -> float:
        """Apply metadata-based score boosts"""
        boosted_score = base_score
        
        # Company match boost
        companies = query_analysis.get("companies", [])
        if companies:
            doc_ticker = doc_metadata.get("ticker", "")
            if any(company.get("ticker") == doc_ticker for company in companies):
                boosted_score *= 1.5
        
        # Filing type match boost
        filing_types = query_analysis.get("filing_types", [])
        if filing_types:
            doc_filing_type = doc_metadata.get("filing_type", "")
            if any(ft.get("filing_type") == doc_filing_type for ft in filing_types):
                boosted_score *= 1.3
        
        # Intent-based boosts
        intent = query_analysis.get("intent", {}).get("primary", "")
        section_name = doc_metadata.get("section_name", "").lower()
        
        if intent == "financial_performance" and "financial" in section_name:
            boosted_score *= 1.4
        elif intent == "risk_factors" and "risk" in section_name:
            boosted_score *= 1.4
        elif intent == "business_operations" and "business" in section_name:
            boosted_score *= 1.2
        
        # Financial data boost
        if doc_metadata.get("has_financial_data", False) and intent == "financial_performance":
            boosted_score *= 1.2
        
        return boosted_score
    
    def _get_matched_keywords(self, query_keywords: List[str], doc_metadata: Dict[str, Any]) -> List[str]:
        """Get list of keywords that matched in the document"""
        content = doc_metadata.get("content_preview", "").lower()
        section_name = doc_metadata.get("section_name", "").lower()
        key_phrases = [phrase.lower() for phrase in doc_metadata.get("key_phrases", [])]
        
        searchable_text = f"{content} {section_name} {' '.join(key_phrases)}"
        
        matched = []
        for keyword in query_keywords:
            if re.search(r'\b' + re.escape(keyword) + r'\b', searchable_text):
                matched.append(keyword)
        
        return matched
    
    def _combine_results(self, semantic_results: List[Dict[str, Any]], 
                        keyword_results: List[Dict[str, Any]], 
                        semantic_weight: float, keyword_weight: float) -> List[Dict[str, Any]]:
        """Combine semantic and keyword search results"""
        combined = {}
        
        # Process semantic results
        for result in semantic_results:
            doc_id = self._get_document_id(result)
            combined[doc_id] = result.copy()
            combined[doc_id]["combined_score"] = result.get("score", 0) * semantic_weight
            combined[doc_id]["has_semantic"] = True
            combined[doc_id]["has_keyword"] = False
        
        # Process keyword results
        for result in keyword_results:
            doc_id = self._get_document_id(result)
            
            if doc_id in combined:
                # Combine scores
                combined[doc_id]["combined_score"] += result.get("keyword_score", 0) * keyword_weight
                combined[doc_id]["has_keyword"] = True
                combined[doc_id]["matched_keywords"] = result.get("matched_keywords", [])
            else:
                # New result from keyword search
                combined[doc_id] = result.copy()
                combined[doc_id]["combined_score"] = result.get("keyword_score", 0) * keyword_weight
                combined[doc_id]["has_semantic"] = False
                combined[doc_id]["has_keyword"] = True
        
        return list(combined.values())
    
    def _get_document_id(self, result: Dict[str, Any]) -> str:
        """Get unique document identifier"""
        metadata = result.get("metadata", {})
        return f"{metadata.get('ticker', 'unknown')}_{metadata.get('accession_number', 'unknown')}_{metadata.get('chunk_id', 0)}"
    
    def _final_ranking(self, results: List[Dict[str, Any]], query_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Apply final ranking to combined results"""
        
        # Sort by combined score
        results.sort(key=lambda x: x.get("combined_score", 0), reverse=True)
        
        # Apply diversity boost (prefer results from different sources)
        seen_sources = set()
        for result in results:
            metadata = result.get("metadata", {})
            source_key = f"{metadata.get('ticker', '')}_{metadata.get('filing_type', '')}"
            
            if source_key not in seen_sources:
                result["combined_score"] *= 1.1  # Diversity boost
                seen_sources.add(source_key)
        
        # Re-sort after diversity boost
        results.sort(key=lambda x: x.get("combined_score", 0), reverse=True)
        
        return results
    
    def _build_metadata_filters(self, query_analysis: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Build metadata filters from query analysis"""
        filters = {}
        
        # Company filters
        companies = query_analysis.get("companies", [])
        if len(companies) == 1:
            filters["ticker"] = companies[0].get("ticker")
        
        # Filing type filters
        filing_types = query_analysis.get("filing_types", [])
        if len(filing_types) == 1:
            filters["filing_type"] = filing_types[0].get("filing_type")
        
        # Intent-based filters
        intent = query_analysis.get("intent", {}).get("primary", "")
        if intent == "financial_performance":
            filters["has_financial_data"] = True
        
        return filters if filters else None


# Global hybrid retrieval instance
hybrid_retrieval = HybridRetrievalSystem()
