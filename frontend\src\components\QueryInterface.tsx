/**
 * Enhanced Query Interface Component
 * Phase 6: Frontend Development - Task 6.1
 */

import React, { useState, useCallback } from 'react';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Collapse,
  IconButton,
  Alert,
  CircularProgress,
  Tooltip,
} from '@mui/material';
import {
  Search as SearchIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Settings as SettingsIcon,
  History as HistoryIcon,
  Clear as ClearIcon,
} from '@mui/icons-material';
import { QueryRequest } from '../types/api';

interface QueryInterfaceProps {
  onSubmit: (query: QueryRequest) => void;
  isLoading?: boolean;
  error?: string;
  supportedCompanies?: string[];
  recentQueries?: string[];
  onClearError?: () => void;
}

const QueryInterface: React.FC<QueryInterfaceProps> = ({
  onSubmit,
  isLoading = false,
  error,
  supportedCompanies = [],
  recentQueries = [],
  onClearError,
}) => {
  // Form state
  const [question, setQuestion] = useState('');
  const [maxChunks, setMaxChunks] = useState(5);
  const [modelPreference, setModelPreference] = useState<'gemma' | 'deepseek' | ''>('');
  const [includeSources, setIncludeSources] = useState(true);
  const [includeConfidence, setIncludeConfidence] = useState(true);
  const [useCache, setUseCache] = useState(true);
  
  // UI state
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [showSamples, setShowSamples] = useState(true);

  // Sample queries
  const sampleQueries = [
    "What are Apple's primary revenue drivers in their latest 10-K?",
    "Compare Microsoft and Google's R&D spending trends",
    "What are Tesla's main risk factors mentioned in recent filings?",
    "How do tech companies describe their AI strategies?",
    "What are the key financial metrics for AAPL in Q3 2023?",
    "Show me Amazon's business segment performance",
  ];

  // Handle form submission
  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    
    if (!question.trim()) {
      return;
    }

    const queryRequest: QueryRequest = {
      question: question.trim(),
      max_chunks: maxChunks,
      model_preference: modelPreference || undefined,
      include_sources: includeSources,
      include_confidence: includeConfidence,
      use_cache: useCache,
      user_id: 'frontend_user', // TODO: Implement proper user management
      session_id: `session_${Date.now()}`, // TODO: Implement proper session management
    };

    onSubmit(queryRequest);
  }, [question, maxChunks, modelPreference, includeSources, includeConfidence, useCache, onSubmit]);

  // Handle sample query selection
  const handleSampleClick = useCallback((sampleQuery: string) => {
    setQuestion(sampleQuery);
    if (onClearError) {
      onClearError();
    }
  }, [onClearError]);

  // Handle recent query selection
  const handleRecentClick = useCallback((recentQuery: string) => {
    setQuestion(recentQuery);
    if (onClearError) {
      onClearError();
    }
  }, [onClearError]);

  // Clear form
  const handleClear = useCallback(() => {
    setQuestion('');
    if (onClearError) {
      onClearError();
    }
  }, [onClearError]);

  return (
    <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
      {/* Error Display */}
      {error && (
        <Alert 
          severity="error" 
          sx={{ mb: 3 }}
          onClose={onClearError}
        >
          {error}
        </Alert>
      )}

      {/* Main Query Form */}
      <form onSubmit={handleSubmit}>
        <Box display="flex" gap={2} alignItems="flex-start" mb={3}>
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Ask a question about SEC filings... (e.g., 'What are Apple's main risk factors?')"
            value={question}
            onChange={(e) => setQuestion(e.target.value)}
            disabled={isLoading}
            multiline
            minRows={2}
            maxRows={6}
            sx={{ flexGrow: 1 }}
          />
          
          <Box display="flex" flexDirection="column" gap={1}>
            <Button
              type="submit"
              variant="contained"
              size="large"
              disabled={!question.trim() || isLoading}
              startIcon={isLoading ? <CircularProgress size={20} /> : <SearchIcon />}
              sx={{ minWidth: 140, height: 56 }}
            >
              {isLoading ? 'Processing...' : 'Ask Question'}
            </Button>
            
            {question && (
              <Tooltip title="Clear query">
                <IconButton
                  onClick={handleClear}
                  disabled={isLoading}
                  size="small"
                >
                  <ClearIcon />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>

        {/* Advanced Settings */}
        <Box display="flex" alignItems="center" gap={2} mb={2}>
          <Button
            startIcon={showAdvanced ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            onClick={() => setShowAdvanced(!showAdvanced)}
            variant="text"
            size="small"
          >
            Advanced Settings
          </Button>
          
          <Button
            startIcon={<HistoryIcon />}
            onClick={() => setShowSamples(!showSamples)}
            variant="text"
            size="small"
          >
            {showSamples ? 'Hide' : 'Show'} Examples
          </Button>
        </Box>

        <Collapse in={showAdvanced}>
          <Box sx={{ p: 3, bgcolor: 'grey.50', borderRadius: 1, mb: 3 }}>
            <Typography variant="h6" gutterBottom startIcon={<SettingsIcon />}>
              Query Configuration
            </Typography>
            
            <Box display="flex" flexWrap="wrap" gap={3}>
              {/* Max Chunks */}
              <FormControl size="small" sx={{ minWidth: 150 }}>
                <InputLabel>Context Chunks</InputLabel>
                <Select
                  value={maxChunks}
                  label="Context Chunks"
                  onChange={(e) => setMaxChunks(Number(e.target.value))}
                  disabled={isLoading}
                >
                  {[1, 3, 5, 7, 10].map((num) => (
                    <MenuItem key={num} value={num}>
                      {num} chunk{num > 1 ? 's' : ''}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* Model Preference */}
              <FormControl size="small" sx={{ minWidth: 150 }}>
                <InputLabel>LLM Model</InputLabel>
                <Select
                  value={modelPreference}
                  label="LLM Model"
                  onChange={(e) => setModelPreference(e.target.value as 'gemma' | 'deepseek' | '')}
                  disabled={isLoading}
                >
                  <MenuItem value="">Auto Select</MenuItem>
                  <MenuItem value="gemma">Gemma 2 9B</MenuItem>
                  <MenuItem value="deepseek">DeepSeek R1</MenuItem>
                </Select>
              </FormControl>

              {/* Switches */}
              <Box display="flex" flexDirection="column" gap={1}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={includeSources}
                      onChange={(e) => setIncludeSources(e.target.checked)}
                      disabled={isLoading}
                      size="small"
                    />
                  }
                  label="Include Sources"
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={includeConfidence}
                      onChange={(e) => setIncludeConfidence(e.target.checked)}
                      disabled={isLoading}
                      size="small"
                    />
                  }
                  label="Show Confidence"
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={useCache}
                      onChange={(e) => setUseCache(e.target.checked)}
                      disabled={isLoading}
                      size="small"
                    />
                  }
                  label="Use Cache"
                />
              </Box>
            </Box>
          </Box>
        </Collapse>
      </form>

      {/* Sample Queries */}
      <Collapse in={showSamples}>
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Try these sample queries:
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
            {sampleQueries.map((sampleQuery, index) => (
              <Chip
                key={index}
                label={sampleQuery}
                variant="outlined"
                clickable
                onClick={() => handleSampleClick(sampleQuery)}
                size="small"
                disabled={isLoading}
                sx={{ 
                  maxWidth: 300,
                  '& .MuiChip-label': {
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }
                }}
              />
            ))}
          </Box>

          {/* Recent Queries */}
          {recentQueries.length > 0 && (
            <>
              <Typography variant="subtitle2" gutterBottom>
                Recent queries:
              </Typography>
              <Box display="flex" flexWrap="wrap" gap={1}>
                {recentQueries.slice(0, 5).map((recentQuery, index) => (
                  <Chip
                    key={index}
                    label={recentQuery}
                    variant="filled"
                    color="secondary"
                    clickable
                    onClick={() => handleRecentClick(recentQuery)}
                    size="small"
                    disabled={isLoading}
                    sx={{ 
                      maxWidth: 300,
                      '& .MuiChip-label': {
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }
                    }}
                  />
                ))}
              </Box>
            </>
          )}
        </Box>
      </Collapse>

      {/* Supported Companies Info */}
      {supportedCompanies.length > 0 && (
        <Box mt={3} p={2} bgcolor="info.light" borderRadius={1}>
          <Typography variant="caption" display="block" gutterBottom>
            Supported Companies ({supportedCompanies.length}):
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {supportedCompanies.slice(0, 10).join(', ')}
            {supportedCompanies.length > 10 && ` and ${supportedCompanies.length - 10} more...`}
          </Typography>
        </Box>
      )}
    </Paper>
  );
};

export default QueryInterface;
