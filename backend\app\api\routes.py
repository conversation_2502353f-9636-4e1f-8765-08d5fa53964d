"""
API Routes Configuration
"""

from fastapi import APIRouter
from app.api.endpoints import health, query, companies, filings, vector_db, batch, admin

# Create main API router
api_router = APIRouter()

# Include endpoint routers
api_router.include_router(health.router, prefix="/health", tags=["health"])
api_router.include_router(query.router, prefix="/query", tags=["query"])
api_router.include_router(companies.router, prefix="/companies", tags=["companies"])
api_router.include_router(filings.router, prefix="/filings", tags=["filings"])
api_router.include_router(vector_db.router, prefix="/vector-db", tags=["vector-database"])
api_router.include_router(batch.router, prefix="/batch", tags=["batch"])
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
