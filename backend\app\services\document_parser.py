"""
SEC Document Parser
Handles parsing of SEC filing HTML documents and text extraction
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from bs4 import BeautifulSoup, Tag, NavigableString
from datetime import datetime
import structlog

logger = structlog.get_logger()


class SECDocumentParser:
    """Parser for SEC filing documents"""
    
    def __init__(self):
        # Common section patterns for different filing types
        self.section_patterns = {
            '10-K': {
                'business': r'(?i)item\s*1[^0-9].*?business',
                'risk_factors': r'(?i)item\s*1a.*?risk\s*factors',
                'properties': r'(?i)item\s*2.*?properties',
                'legal_proceedings': r'(?i)item\s*3.*?legal\s*proceedings',
                'controls_procedures': r'(?i)item\s*4.*?controls\s*and\s*procedures',
                'market_risk': r'(?i)item\s*7a.*?market\s*risk',
                'financial_statements': r'(?i)item\s*8.*?financial\s*statements',
                'management_discussion': r'(?i)item\s*7.*?management.*?discussion'
            },
            '10-Q': {
                'financial_statements': r'(?i)part\s*i.*?item\s*1.*?financial\s*statements',
                'management_discussion': r'(?i)part\s*i.*?item\s*2.*?management.*?discussion',
                'controls_procedures': r'(?i)part\s*i.*?item\s*4.*?controls\s*and\s*procedures',
                'legal_proceedings': r'(?i)part\s*ii.*?item\s*1.*?legal\s*proceedings',
                'risk_factors': r'(?i)part\s*ii.*?item\s*1a.*?risk\s*factors'
            },
            '8-K': {
                'regulation_fd': r'(?i)item\s*7\.01.*?regulation\s*fd',
                'material_agreements': r'(?i)item\s*1\.01.*?material\s*agreements',
                'acquisition_disposition': r'(?i)item\s*2\.01.*?acquisition.*?disposition',
                'bankruptcy': r'(?i)item\s*3\.01.*?bankruptcy',
                'financial_statements': r'(?i)item\s*9\.01.*?financial\s*statements'
            },
            'DEF 14A': {
                'executive_compensation': r'(?i)executive\s*compensation',
                'director_compensation': r'(?i)director\s*compensation',
                'corporate_governance': r'(?i)corporate\s*governance',
                'audit_committee': r'(?i)audit\s*committee',
                'proxy_statement': r'(?i)proxy\s*statement'
            }
        }
    
    def parse_document(self, html_content: str, filing_type: str, 
                      ticker: str, filing_date: str) -> Dict[str, Any]:
        """
        Parse SEC filing HTML document and extract structured information
        
        Args:
            html_content: Raw HTML content of the filing
            filing_type: Type of filing (10-K, 10-Q, 8-K, DEF 14A)
            ticker: Company ticker symbol
            filing_date: Filing date
            
        Returns:
            Dictionary containing parsed document structure
        """
        try:
            logger.info("Parsing SEC document", 
                       filing_type=filing_type, 
                       ticker=ticker,
                       content_length=len(html_content))
            
            # Parse HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Extract basic metadata
            metadata = self._extract_metadata(soup, filing_type, ticker, filing_date)
            
            # Extract document structure
            sections = self._extract_sections(soup, filing_type)
            
            # Extract tables
            tables = self._extract_tables(soup)
            
            # Extract text content
            text_content = self._extract_text_content(soup)
            
            # Clean and structure the content
            cleaned_sections = self._clean_sections(sections)
            
            result = {
                'metadata': metadata,
                'sections': cleaned_sections,
                'tables': tables,
                'full_text': text_content,
                'parsing_stats': {
                    'sections_found': len(cleaned_sections),
                    'tables_found': len(tables),
                    'total_text_length': len(text_content),
                    'parsing_timestamp': datetime.utcnow().isoformat()
                }
            }
            
            logger.info("Document parsing completed", 
                       ticker=ticker,
                       sections_found=len(cleaned_sections),
                       tables_found=len(tables))
            
            return result
            
        except Exception as e:
            logger.error("Document parsing failed", 
                        ticker=ticker, 
                        filing_type=filing_type, 
                        error=str(e))
            raise
    
    def _extract_metadata(self, soup: BeautifulSoup, filing_type: str, 
                         ticker: str, filing_date: str) -> Dict[str, Any]:
        """Extract document metadata"""
        metadata = {
            'ticker': ticker,
            'filing_type': filing_type,
            'filing_date': filing_date,
            'company_name': None,
            'cik': None,
            'document_title': None,
            'period_end_date': None
        }
        
        # Try to extract company name from title or header
        title_tag = soup.find('title')
        if title_tag:
            metadata['document_title'] = title_tag.get_text().strip()
        
        # Look for company name in common locations
        company_patterns = [
            r'(?i)company\s*name[:\s]*([^\n\r]+)',
            r'(?i)registrant[:\s]*([^\n\r]+)',
            r'(?i)issuer[:\s]*([^\n\r]+)'
        ]
        
        text_content = soup.get_text()
        for pattern in company_patterns:
            match = re.search(pattern, text_content)
            if match:
                metadata['company_name'] = match.group(1).strip()
                break
        
        # Look for CIK
        cik_pattern = r'(?i)cik[:\s]*(\d+)'
        cik_match = re.search(cik_pattern, text_content)
        if cik_match:
            metadata['cik'] = cik_match.group(1)
        
        # Look for period end date
        period_patterns = [
            r'(?i)period\s*end(?:ed|ing)?\s*(?:date)?[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
            r'(?i)as\s*of\s*(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
            r'(?i)for\s*the\s*(?:quarter|year)\s*end(?:ed|ing)?\s*(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})'
        ]
        
        for pattern in period_patterns:
            match = re.search(pattern, text_content)
            if match:
                metadata['period_end_date'] = match.group(1)
                break
        
        return metadata
    
    def _extract_sections(self, soup: BeautifulSoup, filing_type: str) -> Dict[str, str]:
        """Extract document sections based on filing type"""
        sections = {}

        # First try HTML-based extraction using headers
        html_sections = self._extract_sections_from_html(soup, filing_type)
        sections.update(html_sections)

        # If no sections found via HTML, try text-based extraction
        if not sections and filing_type in self.section_patterns:
            text_sections = self._extract_sections_from_text(soup, filing_type)
            sections.update(text_sections)

        return sections

    def _extract_sections_from_html(self, soup: BeautifulSoup, filing_type: str) -> Dict[str, str]:
        """Extract sections using HTML structure (headers, etc.)"""
        sections = {}

        # Look for common header patterns
        header_tags = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])

        for header in header_tags:
            header_text = header.get_text().strip().lower()

            # Map header text to section names
            section_name = self._map_header_to_section(header_text, filing_type)

            if section_name:
                # Extract content after this header until next header of same or higher level
                content = self._extract_content_after_header(header)
                if content and len(content) > 100:
                    sections[section_name] = content

        return sections

    def _extract_sections_from_text(self, soup: BeautifulSoup, filing_type: str) -> Dict[str, str]:
        """Extract sections using text pattern matching"""
        sections = {}

        text_content = soup.get_text()
        patterns = self.section_patterns[filing_type]

        for section_name, pattern in patterns.items():
            try:
                # Find section start
                match = re.search(pattern, text_content, re.IGNORECASE | re.MULTILINE)
                if match:
                    start_pos = match.start()

                    # Find section end (next item or end of document)
                    next_item_pattern = r'(?i)item\s*\d+[a-z]?[.\s]'
                    next_match = re.search(next_item_pattern, text_content[start_pos + len(match.group()):])

                    if next_match:
                        end_pos = start_pos + len(match.group()) + next_match.start()
                    else:
                        end_pos = len(text_content)

                    section_text = text_content[start_pos:end_pos].strip()
                    if len(section_text) > 100:  # Only include substantial sections
                        sections[section_name] = section_text

            except Exception as e:
                logger.warning("Failed to extract section",
                             section=section_name,
                             error=str(e))

        return sections

    def _map_header_to_section(self, header_text: str, filing_type: str) -> Optional[str]:
        """Map header text to standardized section names"""
        header_mappings = {
            '10-K': {
                'business': ['business', 'item 1', 'item 1.'],
                'risk_factors': ['risk factors', 'item 1a', 'item 1a.'],
                'properties': ['properties', 'item 2', 'item 2.'],
                'legal_proceedings': ['legal proceedings', 'item 3', 'item 3.'],
                'management_discussion': ['management', 'discussion', 'analysis', 'item 7', 'item 7.'],
                'financial_statements': ['financial statements', 'item 8', 'item 8.']
            },
            '10-Q': {
                'financial_statements': ['financial statements', 'item 1', 'part i'],
                'management_discussion': ['management', 'discussion', 'analysis', 'item 2'],
                'controls_procedures': ['controls', 'procedures', 'item 4'],
                'legal_proceedings': ['legal proceedings', 'part ii'],
                'risk_factors': ['risk factors', 'item 1a']
            },
            '8-K': {
                'material_agreements': ['material agreements', 'item 1.01'],
                'acquisition_disposition': ['acquisition', 'disposition', 'item 2.01'],
                'financial_statements': ['financial statements', 'item 9.01']
            }
        }

        if filing_type not in header_mappings:
            return None

        mappings = header_mappings[filing_type]

        for section_name, keywords in mappings.items():
            for keyword in keywords:
                if keyword in header_text:
                    return section_name

        return None

    def _extract_content_after_header(self, header: Tag) -> str:
        """Extract content after a header until the next header of same or higher level"""
        content_parts = []
        current = header.next_sibling
        header_level = int(header.name[1]) if header.name.startswith('h') else 6

        while current:
            if hasattr(current, 'name'):
                # If we hit another header of same or higher level, stop
                if (current.name and current.name.startswith('h') and
                    int(current.name[1]) <= header_level):
                    break

                # Extract text from this element
                if current.name not in ['script', 'style']:
                    text = current.get_text().strip()
                    if text:
                        content_parts.append(text)
            else:
                # Text node
                text = str(current).strip()
                if text:
                    content_parts.append(text)

            current = current.next_sibling

        return ' '.join(content_parts)
    
    def _extract_tables(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract tables from the document"""
        tables = []
        
        for i, table in enumerate(soup.find_all('table')):
            try:
                # Extract table data
                rows = []
                for row in table.find_all('tr'):
                    cells = []
                    for cell in row.find_all(['td', 'th']):
                        cell_text = cell.get_text().strip()
                        cells.append(cell_text)
                    if cells:  # Only add non-empty rows
                        rows.append(cells)
                
                if rows and len(rows) > 1:  # Must have header and at least one data row
                    table_data = {
                        'table_id': i,
                        'rows': rows,
                        'row_count': len(rows),
                        'column_count': len(rows[0]) if rows else 0
                    }
                    tables.append(table_data)
                    
            except Exception as e:
                logger.warning("Failed to extract table", table_id=i, error=str(e))
        
        return tables
    
    def _extract_text_content(self, soup: BeautifulSoup) -> str:
        """Extract clean text content from the document"""
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()
        
        # Get text and clean it
        text = soup.get_text()
        
        # Clean up whitespace
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)
        
        return text
    
    def _clean_sections(self, sections: Dict[str, str]) -> Dict[str, str]:
        """Clean and normalize section text"""
        cleaned_sections = {}
        
        for section_name, section_text in sections.items():
            # Remove excessive whitespace
            cleaned_text = re.sub(r'\s+', ' ', section_text)
            
            # Remove page numbers and headers/footers
            cleaned_text = re.sub(r'(?i)page\s*\d+\s*of\s*\d+', '', cleaned_text)
            cleaned_text = re.sub(r'(?i)table\s*of\s*contents', '', cleaned_text)
            
            # Remove excessive punctuation
            cleaned_text = re.sub(r'[.]{3,}', '...', cleaned_text)
            cleaned_text = re.sub(r'[-]{3,}', '---', cleaned_text)
            
            # Trim and store
            cleaned_text = cleaned_text.strip()
            if len(cleaned_text) > 50:  # Only keep substantial content
                cleaned_sections[section_name] = cleaned_text
        
        return cleaned_sections
    
    def extract_key_information(self, parsed_document: Dict[str, Any]) -> Dict[str, Any]:
        """Extract key information from parsed document for quick access"""
        key_info = {
            'ticker': parsed_document['metadata']['ticker'],
            'filing_type': parsed_document['metadata']['filing_type'],
            'company_name': parsed_document['metadata']['company_name'],
            'filing_date': parsed_document['metadata']['filing_date'],
            'period_end_date': parsed_document['metadata']['period_end_date'],
            'sections_available': list(parsed_document['sections'].keys()),
            'has_financial_data': 'financial_statements' in parsed_document['sections'],
            'has_risk_factors': 'risk_factors' in parsed_document['sections'],
            'document_length': parsed_document['parsing_stats']['total_text_length'],
            'summary': self._generate_summary(parsed_document)
        }
        
        return key_info
    
    def _generate_summary(self, parsed_document: Dict[str, Any]) -> str:
        """Generate a brief summary of the document"""
        sections = parsed_document['sections']
        filing_type = parsed_document['metadata']['filing_type']
        
        summary_parts = []
        
        if filing_type == '10-K':
            summary_parts.append("Annual report containing comprehensive business overview")
        elif filing_type == '10-Q':
            summary_parts.append("Quarterly report with financial updates")
        elif filing_type == '8-K':
            summary_parts.append("Current report announcing material events")
        elif filing_type == 'DEF 14A':
            summary_parts.append("Proxy statement for shareholder meetings")
        
        if 'risk_factors' in sections:
            summary_parts.append("includes risk factor disclosures")
        
        if 'financial_statements' in sections:
            summary_parts.append("contains financial statements")
        
        summary_parts.append(f"with {len(sections)} major sections")
        
        return ", ".join(summary_parts).capitalize() + "."


# Global parser instance
document_parser = SECDocumentParser()
