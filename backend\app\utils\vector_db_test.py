"""
Vector Database Testing Utilities
"""

import asyncio
import random
from typing import List, Dict, Any
import structlog

from app.services.vector_db import VectorDBManager

logger = structlog.get_logger()


async def test_vector_db_connection(use_pinecone: bool = False) -> Dict[str, Any]:
    """Test vector database connection and basic operations"""
    
    results = {
        "connection": False,
        "create_test": False,
        "query_test": False,
        "stats_test": False,
        "db_type": None,
        "errors": []
    }
    
    try:
        # Initialize vector database
        db_manager = VectorDBManager()
        
        logger.info("Testing vector database connection", use_pinecone=use_pinecone)
        
        # Test connection
        connection_success = await db_manager.initialize(use_pinecone=use_pinecone)
        results["connection"] = connection_success
        results["db_type"] = db_manager.db_type
        
        if not connection_success:
            results["errors"].append("Failed to connect to vector database")
            return results
        
        # Test basic operations
        await test_basic_operations(db_manager, results)
        
        logger.info("Vector database test completed", results=results)
        return results
        
    except Exception as e:
        error_msg = f"Vector database test failed: {str(e)}"
        logger.error(error_msg)
        results["errors"].append(error_msg)
        return results


async def test_basic_operations(db_manager: VectorDBManager, results: Dict[str, Any]):
    """Test basic vector database operations"""
    
    try:
        # Generate test data
        test_vectors = generate_test_vectors(5)
        
        # Test storing vectors
        logger.info("Testing vector storage")
        store_success = await db_manager.store_document_chunks(test_vectors)
        results["create_test"] = store_success
        
        if not store_success:
            results["errors"].append("Failed to store test vectors")
            return
        
        # Test querying vectors
        logger.info("Testing vector query")
        query_embedding = test_vectors[0]["embedding"]  # Use first vector as query
        
        search_results = await db_manager.search_similar_chunks(
            query_embedding=query_embedding,
            top_k=3
        )
        
        results["query_test"] = len(search_results) > 0
        
        if len(search_results) == 0:
            results["errors"].append("No results returned from vector query")
        else:
            logger.info("Query test successful", results_count=len(search_results))
        
        # Test health check / stats
        logger.info("Testing database stats")
        health_check = await db_manager.health_check()
        results["stats_test"] = health_check.get("status") == "healthy"
        
        if health_check.get("status") != "healthy":
            results["errors"].append(f"Health check failed: {health_check}")
        
    except Exception as e:
        error_msg = f"Basic operations test failed: {str(e)}"
        logger.error(error_msg)
        results["errors"].append(error_msg)


def generate_test_vectors(count: int = 5) -> List[Dict[str, Any]]:
    """Generate test vectors for database testing"""
    
    test_vectors = []
    
    for i in range(count):
        # Generate random embedding vector (1536 dimensions for OpenAI compatibility)
        embedding = [random.random() for _ in range(1536)]
        
        # Create test metadata
        metadata = {
            "id": f"test_doc_{i}",
            "embedding": embedding,
            "text": f"This is test document number {i} for vector database testing.",
            "ticker": "TEST",
            "filing_type": "10-K",
            "filing_date": "2023-01-01",
            "section": "Risk Factors",
            "chunk_index": i,
            "source_url": f"https://example.com/test_doc_{i}.html"
        }
        
        test_vectors.append(metadata)
    
    return test_vectors


async def test_faiss_only() -> Dict[str, Any]:
    """Test FAISS database specifically"""
    logger.info("Testing FAISS vector database")
    return await test_vector_db_connection(use_pinecone=False)


async def test_pinecone_only() -> Dict[str, Any]:
    """Test Pinecone database specifically (requires valid API keys)"""
    logger.info("Testing Pinecone vector database")
    return await test_vector_db_connection(use_pinecone=True)


async def run_comprehensive_test() -> Dict[str, Any]:
    """Run comprehensive vector database tests"""
    
    logger.info("Starting comprehensive vector database tests")
    
    comprehensive_results = {
        "faiss_test": {},
        "pinecone_test": {},
        "summary": {
            "faiss_working": False,
            "pinecone_working": False,
            "recommended_db": None
        }
    }
    
    # Test FAISS (always available)
    faiss_results = await test_faiss_only()
    comprehensive_results["faiss_test"] = faiss_results
    comprehensive_results["summary"]["faiss_working"] = faiss_results["connection"]
    
    # Test Pinecone (only if API keys are configured)
    from app.core.config import settings
    if (settings.PINECONE_API_KEY != "placeholder-pinecone-key" and 
        settings.PINECONE_ENVIRONMENT != "placeholder-environment"):
        
        pinecone_results = await test_pinecone_only()
        comprehensive_results["pinecone_test"] = pinecone_results
        comprehensive_results["summary"]["pinecone_working"] = pinecone_results["connection"]
    else:
        comprehensive_results["pinecone_test"] = {
            "connection": False,
            "errors": ["Pinecone API keys not configured"]
        }
    
    # Determine recommendation
    if comprehensive_results["summary"]["pinecone_working"]:
        comprehensive_results["summary"]["recommended_db"] = "pinecone"
    elif comprehensive_results["summary"]["faiss_working"]:
        comprehensive_results["summary"]["recommended_db"] = "faiss"
    else:
        comprehensive_results["summary"]["recommended_db"] = "none_working"
    
    logger.info("Comprehensive vector database tests completed", 
                summary=comprehensive_results["summary"])
    
    return comprehensive_results


if __name__ == "__main__":
    # Run tests when script is executed directly
    async def main():
        results = await run_comprehensive_test()
        print("\n" + "="*50)
        print("VECTOR DATABASE TEST RESULTS")
        print("="*50)
        
        print(f"\nFAISS Test Results:")
        print(f"  Connection: {'✅' if results['faiss_test']['connection'] else '❌'}")
        print(f"  Storage: {'✅' if results['faiss_test']['create_test'] else '❌'}")
        print(f"  Query: {'✅' if results['faiss_test']['query_test'] else '❌'}")
        print(f"  Stats: {'✅' if results['faiss_test']['stats_test'] else '❌'}")
        
        print(f"\nPinecone Test Results:")
        print(f"  Connection: {'✅' if results['pinecone_test']['connection'] else '❌'}")
        if results['pinecone_test']['connection']:
            print(f"  Storage: {'✅' if results['pinecone_test']['create_test'] else '❌'}")
            print(f"  Query: {'✅' if results['pinecone_test']['query_test'] else '❌'}")
            print(f"  Stats: {'✅' if results['pinecone_test']['stats_test'] else '❌'}")
        
        print(f"\nRecommended Database: {results['summary']['recommended_db'].upper()}")
        
        if results['faiss_test'].get('errors'):
            print(f"\nFAISS Errors: {results['faiss_test']['errors']}")
        
        if results['pinecone_test'].get('errors'):
            print(f"\nPinecone Errors: {results['pinecone_test']['errors']}")
    
    asyncio.run(main())
