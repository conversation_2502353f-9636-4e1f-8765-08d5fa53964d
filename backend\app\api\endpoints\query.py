"""
Query API Endpoints
Handles Q&A functionality using RAG (Retrieval-Augmented Generation)
"""

from typing import Optional, List, Dict, Any
from fastapi import APIRouter, HTTPException, Query as QueryParam
from pydantic import BaseModel, Field
import structlog

from app.services.query_engine import query_engine
from app.services.llm_service import llm_service
from app.services.query_cache import query_cache
from app.services.query_history import query_history

logger = structlog.get_logger()
router = APIRouter()


class QueryRequest(BaseModel):
    """Request model for Q&A queries"""
    question: str = Field(..., description="User's question about SEC filings", min_length=1, max_length=1000)
    max_chunks: int = Field(default=5, description="Maximum number of context chunks to retrieve", ge=1, le=20)
    model_preference: Optional[str] = Field(default=None, description="Preferred LLM model (gemma or deepseek)")
    include_sources: bool = Field(default=True, description="Include detailed source information in response")
    include_confidence: bool = Field(default=True, description="Include confidence assessment in response")
    use_cache: bool = Field(default=True, description="Use cached results if available")
    user_id: Optional[str] = Field(default=None, description="User identifier for history tracking")
    session_id: Optional[str] = Field(default=None, description="Session identifier for history tracking")


class Source(BaseModel):
    """Source attribution model"""
    ticker: str
    filing_type: str
    filing_date: str
    section: str
    accession_number: Optional[str] = None
    relevance_score: Optional[float] = None
    has_financial_data: Optional[bool] = None


class QueryResponse(BaseModel):
    """Response model for Q&A queries"""
    success: bool
    answer: Optional[str] = None
    sources: Optional[List[Source]] = None
    confidence: Optional[str] = None
    processing_time: Optional[float] = None
    model_used: Optional[str] = None
    context_chunks: Optional[int] = None
    error: Optional[str] = None
    suggestions: Optional[List[str]] = None


@router.post("/", response_model=QueryResponse)
async def process_query(request: QueryRequest):
    """
    Process a natural language query about SEC filings using RAG

    This endpoint processes natural language questions about SEC filings and returns
    comprehensive answers with source attribution and confidence assessment.
    """
    logger.info("Processing Q&A request",
               question_length=len(request.question),
               max_chunks=request.max_chunks,
               model_preference=request.model_preference,
               use_cache=request.use_cache,
               user_id=request.user_id)

    try:
        # Validate model preference
        if request.model_preference and request.model_preference not in ["gemma", "deepseek"]:
            raise HTTPException(
                status_code=400,
                detail="Invalid model preference. Must be 'gemma' or 'deepseek'"
            )

        # Check cache first if enabled
        cached_result = None
        if request.use_cache:
            cache_filters = {
                "max_chunks": request.max_chunks,
                "model_preference": request.model_preference,
                "include_sources": request.include_sources,
                "include_confidence": request.include_confidence
            }
            cached_result = await query_cache.get(request.question, cache_filters)

        if cached_result:
            # Use cached result
            logger.info("Using cached result", question_length=len(request.question))
            result = cached_result
        else:
            # Process the query using the query engine
            result = await query_engine.process_query(
                query=request.question,
                max_chunks=request.max_chunks,
                model_preference=request.model_preference
            )

            # Cache the result if successful and caching is enabled
            if request.use_cache and result.get("success", False):
                await query_cache.set(request.question, result, cache_filters, ttl_hours=24)

        # Convert to API response format
        if result["success"]:
            # Convert sources to API format
            api_sources = []
            for source in result.get("sources", []):
                api_sources.append(Source(
                    ticker=source.get("ticker", "Unknown"),
                    filing_type=source.get("filing_type", "Unknown"),
                    filing_date=source.get("filing_date", "Unknown"),
                    section=source.get("section_name", "Unknown"),
                    accession_number=source.get("accession_number"),
                    relevance_score=source.get("score"),
                    has_financial_data=source.get("has_financial_data")
                ))

            response = QueryResponse(
                success=True,
                answer=result["answer"],
                sources=api_sources if request.include_sources else None,
                confidence=result.get("confidence_indicators", {}).get("overall") if request.include_confidence else None,
                processing_time=result.get("total_processing_time"),
                model_used=result.get("model_used"),
                context_chunks=result.get("context_chunks_retrieved")
            )
        else:
            response = QueryResponse(
                success=False,
                error=result.get("error"),
                suggestions=result.get("suggestions"),
                processing_time=result.get("processing_time")
            )

        # Add to query history
        try:
            await query_history.add_query(
                query=request.question,
                response=response.dict(),
                user_id=request.user_id,
                session_id=request.session_id
            )
        except Exception as e:
            logger.error("Failed to add query to history", error=str(e))

        logger.info("Q&A request processed successfully",
                   success=result["success"],
                   processing_time=result.get("total_processing_time", 0),
                   cached=cached_result is not None)

        return response

    except Exception as e:
        logger.error("Q&A request failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Query processing failed: {str(e)}"
        )


@router.get("/status")
async def get_engine_status():
    """
    Get the current status of the query processing engine

    Returns information about supported companies, filing types, available models,
    and the overall health of the Q&A system.
    """
    try:
        status = query_engine.get_engine_status()
        logger.info("Engine status requested")
        return status

    except Exception as e:
        logger.error("Failed to get engine status", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get engine status: {str(e)}"
        )


@router.get("/models")
async def get_available_models():
    """
    Get list of available LLM models

    Returns information about the LLM models available for answer generation,
    including their capabilities and current status.
    """
    try:
        models = await llm_service.get_available_models()
        service_status = llm_service.get_service_status()

        return {
            "available_models": models,
            "service_status": service_status,
            "default_model": service_status.get("primary_model"),
            "fallback_model": service_status.get("fallback_model")
        }

    except Exception as e:
        logger.error("Failed to get available models", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get available models: {str(e)}"
        )


@router.get("/companies")
async def get_supported_companies():
    """
    Get list of supported companies

    Returns the list of companies for which SEC filing data is available
    for question answering.
    """
    try:
        status = query_engine.get_engine_status()

        return {
            "companies": status["companies"],
            "total_count": status["supported_companies"],
            "filing_types": status["filing_types"],
            "supported_intents": status["query_intents"]
        }

    except Exception as e:
        logger.error("Failed to get supported companies", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get supported companies: {str(e)}"
        )
