#!/usr/bin/env python3
"""
Server Startup Script
Starts both backend and frontend servers for testing
"""

import subprocess
import sys
import os
import time
import signal
import threading
from pathlib import Path

class ServerManager:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True
        
    def start_backend(self):
        """Start the FastAPI backend server"""
        print("🚀 Starting Backend Server...")
        
        backend_dir = Path("backend")
        if not backend_dir.exists():
            print("❌ Backend directory not found!")
            return False
            
        try:
            # Change to backend directory and start uvicorn
            self.backend_process = subprocess.Popen(
                [sys.executable, "-m", "uvicorn", "app.main:app", "--reload", "--host", "127.0.0.1", "--port", "8000"],
                cwd=backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait a moment and check if it started
            time.sleep(3)
            if self.backend_process.poll() is None:
                print("✅ Backend Server: STARTED (http://127.0.0.1:8000)")
                print("📚 API Docs: http://127.0.0.1:8000/docs")
                return True
            else:
                stdout, stderr = self.backend_process.communicate()
                print("❌ Backend Server: FAILED TO START")
                print(f"Error: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Backend Server Error: {str(e)}")
            return False
    
    def start_frontend(self):
        """Start the React frontend development server"""
        print("🌐 Starting Frontend Server...")
        
        frontend_dir = Path("frontend")
        if not frontend_dir.exists():
            print("❌ Frontend directory not found!")
            return False
            
        # Check if node_modules exists
        if not (frontend_dir / "node_modules").exists():
            print("📦 Installing frontend dependencies...")
            try:
                subprocess.run(["npm", "install"], cwd=frontend_dir, check=True)
            except subprocess.CalledProcessError:
                print("❌ Failed to install frontend dependencies")
                return False
        
        try:
            # Start the Vite dev server
            self.frontend_process = subprocess.Popen(
                ["npm", "run", "dev"],
                cwd=frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for frontend to start (it takes longer)
            print("⏳ Waiting for frontend to start...")
            time.sleep(8)
            
            if self.frontend_process.poll() is None:
                print("✅ Frontend Server: STARTED (http://localhost:5173)")
                return True
            else:
                stdout, stderr = self.frontend_process.communicate()
                print("❌ Frontend Server: FAILED TO START")
                print(f"Error: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Frontend Server Error: {str(e)}")
            return False
    
    def monitor_servers(self):
        """Monitor server processes and restart if needed"""
        while self.running:
            time.sleep(5)
            
            # Check backend
            if self.backend_process and self.backend_process.poll() is not None:
                print("⚠️ Backend server stopped unexpectedly")
                
            # Check frontend
            if self.frontend_process and self.frontend_process.poll() is not None:
                print("⚠️ Frontend server stopped unexpectedly")
    
    def stop_servers(self):
        """Stop both servers gracefully"""
        print("\n🛑 Stopping servers...")
        self.running = False
        
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("✅ Backend server stopped")
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
                print("🔪 Backend server force killed")
            except Exception as e:
                print(f"❌ Error stopping backend: {e}")
        
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print("✅ Frontend server stopped")
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
                print("🔪 Frontend server force killed")
            except Exception as e:
                print(f"❌ Error stopping frontend: {e}")
    
    def run(self):
        """Main run method"""
        print("🎯 SEC Filing QA Agent - Server Startup")
        print("=" * 50)
        print()
        
        # Start backend
        if not self.start_backend():
            print("❌ Failed to start backend server")
            return False
        
        print()
        
        # Start frontend
        if not self.start_frontend():
            print("❌ Failed to start frontend server")
            self.stop_servers()
            return False
        
        print()
        print("🎉 BOTH SERVERS STARTED SUCCESSFULLY!")
        print("=" * 50)
        print("🌐 Frontend: http://localhost:5173")
        print("🔧 Backend API: http://127.0.0.1:8000")
        print("📚 API Docs: http://127.0.0.1:8000/docs")
        print("=" * 50)
        print()
        print("💡 TESTING INSTRUCTIONS:")
        print("1. Open http://localhost:5173 in your browser")
        print("2. Try asking a question like: 'What are Apple's main revenue sources?'")
        print("3. Test advanced features: history, bookmarks, export")
        print("4. Test mobile responsiveness by resizing browser")
        print()
        print("🧪 RUN INTEGRATION TESTS:")
        print("   python test_integration_complete.py")
        print()
        print("Press Ctrl+C to stop both servers")
        print("-" * 50)
        
        # Start monitoring in background
        monitor_thread = threading.Thread(target=self.monitor_servers, daemon=True)
        monitor_thread.start()
        
        try:
            # Keep the script running
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n⚠️ Shutdown requested by user")
        finally:
            self.stop_servers()
        
        return True

def main():
    """Main function"""
    # Handle Ctrl+C gracefully
    server_manager = ServerManager()
    
    def signal_handler(signum, frame):
        print("\n⚠️ Received interrupt signal")
        server_manager.stop_servers()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    # Check prerequisites
    print("🔍 Checking prerequisites...")
    
    # Check Python
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    
    # Check if directories exist
    if not Path("backend").exists():
        print("❌ Backend directory not found")
        return False
        
    if not Path("frontend").exists():
        print("❌ Frontend directory not found")
        return False
    
    # Check if npm is available
    try:
        subprocess.run(["npm", "--version"], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ npm not found. Please install Node.js")
        return False
    
    print("✅ Prerequisites check passed")
    print()
    
    # Run the server manager
    return server_manager.run()

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
    
    print("\n👋 Servers stopped. Goodbye!")
