#!/usr/bin/env python3
"""
Cross-Platform Server Launcher
Detects OS and runs the appropriate startup script
"""

import os
import sys
import platform
import subprocess

def detect_os():
    """Detect the operating system"""
    system = platform.system().lower()
    if system == "windows":
        return "windows"
    elif system in ["linux", "darwin"]:  # darwin is macOS
        return "unix"
    else:
        return "unknown"

def run_windows_script():
    """Run Windows batch script"""
    print("🪟 Detected Windows - Running batch script...")
    try:
        # Try PowerShell first (more modern)
        if os.path.exists("start-servers.ps1"):
            subprocess.run(["powershell", "-ExecutionPolicy", "Bypass", "-File", "start-servers.ps1"], check=True)
        else:
            # Fall back to batch file
            subprocess.run(["start-servers.bat"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running Windows script: {e}")
        return False
    except FileNotFoundError:
        print("❌ PowerShell not found, trying batch file...")
        try:
            subprocess.run(["start-servers.bat"], check=True)
        except Exception as e:
            print(f"❌ Error running batch script: {e}")
            return False
    return True

def run_unix_script():
    """Run Unix/Linux/macOS shell script"""
    system_name = "🐧 Linux" if platform.system() == "Linux" else "🍎 macOS"
    print(f"{system_name} detected - Running shell script...")
    
    # Make script executable
    try:
        os.chmod("start-servers.sh", 0o755)
    except Exception:
        pass
    
    try:
        subprocess.run(["bash", "start-servers.sh"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running shell script: {e}")
        return False
    except FileNotFoundError:
        print("❌ Bash not found, trying sh...")
        try:
            subprocess.run(["sh", "start-servers.sh"], check=True)
        except Exception as e:
            print(f"❌ Error running shell script: {e}")
            return False
    return True

def main():
    """Main launcher function"""
    print("🚀 SEC Filing QA Agent - Cross-Platform Launcher")
    print("=" * 50)
    print()
    
    # Detect OS
    os_type = detect_os()
    
    if os_type == "windows":
        success = run_windows_script()
    elif os_type == "unix":
        success = run_unix_script()
    else:
        print(f"❌ Unsupported operating system: {platform.system()}")
        print("Please run the appropriate script manually:")
        print("  Windows: start-servers.bat or start-servers.ps1")
        print("  Linux/macOS: ./start-servers.sh")
        return False
    
    if not success:
        print("\n❌ Failed to start servers automatically.")
        print("\n🔧 Manual startup options:")
        print("  Windows: start-servers.bat")
        print("  PowerShell: .\\start-servers.ps1")
        print("  Linux/macOS: ./start-servers.sh")
        print("\n📋 Or start manually:")
        print("  Backend:  cd backend && python -m uvicorn app.main:app --reload")
        print("  Frontend: cd frontend && npm run dev")
        return False
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
