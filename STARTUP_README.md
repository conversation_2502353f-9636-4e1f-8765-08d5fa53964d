# 🚀 Server Startup Scripts

Multiple cross-platform scripts to start both your FastAPI backend and React frontend servers simultaneously.

## 🎯 **Quick Start (Recommended)**

### **Option 1: Auto-Detect OS**
```bash
python start.py
```
*Automatically detects your OS and runs the appropriate script*

### **Option 2: Platform-Specific Scripts**

**Windows (Command Prompt):**
```cmd
start-servers.bat
```

**Windows (PowerShell):**
```powershell
.\start-servers.ps1
```

**Linux/macOS (Terminal):**
```bash
./start-servers.sh
```

## 📋 **What These Scripts Do**

1. **✅ Check Prerequisites**
   - Verify Python 3.8+ is installed
   - Verify Node.js/npm is installed
   - Check if project directories exist

2. **📦 Install Dependencies**
   - Create Python virtual environment (if needed)
   - Install backend Python packages
   - Install frontend npm packages

3. **🚀 Start Both Servers**
   - **Backend**: FastAPI on http://127.0.0.1:8000
   - **Frontend**: React on http://localhost:5173

4. **📊 Provide Status & Instructions**
   - Show server URLs
   - Provide testing instructions
   - Show how to run tests

## 🖥️ **Platform-Specific Features**

### **Windows (.bat)**
- Opens servers in separate Command Prompt windows
- Easy to see logs for each server
- Simple to close individual servers

### **Windows (.ps1)**
- Advanced PowerShell features
- Better error handling
- Colored output
- Background job management

### **Linux/macOS (.sh)**
- Runs servers as background processes
- Unified log files (backend.log, frontend.log)
- Graceful shutdown with Ctrl+C
- Process monitoring

## 🧪 **After Starting Servers**

Once servers are running, you can:

### **1. Test the Web Interface**
- Open: http://localhost:5173
- Try asking: "What are Apple's main revenue sources?"

### **2. Test the API Directly**
- API Docs: http://127.0.0.1:8000/docs
- Health Check: http://127.0.0.1:8000/api/v1/health/

### **3. Run Automated Tests**
```bash
# Quick verification
python quick_test.py

# Comprehensive integration test
python test_integration_complete.py

# Frontend E2E test (requires puppeteer)
cd frontend && node test_phase6_comprehensive.js
```

## 🔧 **Troubleshooting**

### **Common Issues**

**"Python not found"**
- Install Python 3.8+ from python.org
- Make sure Python is in your PATH

**"npm not found"**
- Install Node.js from nodejs.org
- Restart terminal after installation

**"Permission denied" (Linux/macOS)**
```bash
chmod +x start-servers.sh
./start-servers.sh
```

**"Execution Policy" error (Windows PowerShell)**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\start-servers.ps1
```

**Servers won't start**
- Check if ports 8000 and 5173 are available
- Close any existing server processes
- Check error messages in terminal

### **Manual Startup (Fallback)**

If scripts don't work, start manually:

**Terminal 1 (Backend):**
```bash
cd backend
python -m venv venv
# Windows: venv\Scripts\activate
# Linux/macOS: source venv/bin/activate
pip install -r requirements.txt
python -m uvicorn app.main:app --reload
```

**Terminal 2 (Frontend):**
```bash
cd frontend
npm install
npm run dev
```

## 📊 **Expected Output**

### **Successful Startup**
```
🎉 SERVERS STARTED!
========================================
🌐 Frontend: http://localhost:5173
🔧 Backend:  http://127.0.0.1:8000
📚 API Docs: http://127.0.0.1:8000/docs
========================================

💡 TESTING INSTRUCTIONS:
1. Open http://localhost:5173 in your browser
2. Try asking: "What are Apple's main revenue sources?"
3. Test advanced features: history, bookmarks, export
```

### **Server Status Check**
```
✅ Backend Server: RUNNING (http://127.0.0.1:8000)
✅ Frontend Server: RUNNING (http://localhost:5173)
```

## 🎯 **Next Steps**

1. **Start Servers**: Run one of the startup scripts
2. **Open Frontend**: http://localhost:5173
3. **Test Query**: Ask about SEC filings
4. **Explore Features**: History, bookmarks, export, mobile UI
5. **Run Tests**: Verify everything works correctly

## 📁 **Script Files**

- `start.py` - Cross-platform Python launcher
- `start-servers.bat` - Windows batch script
- `start-servers.ps1` - Windows PowerShell script
- `start-servers.sh` - Linux/macOS shell script
- `quick_test.py` - Quick server verification
- `test_integration_complete.py` - Full integration test

## 🎉 **Ready to Go!**

Your SEC Filing QA Agent is ready for testing with:
- ✅ **FastAPI Backend** with advanced features
- ✅ **React Frontend** with mobile responsiveness  
- ✅ **Cross-Platform Scripts** for easy startup
- ✅ **Comprehensive Testing** suite
- ✅ **Production-Ready** architecture

**Choose your preferred startup method and let's test your AI-powered SEC filing analysis tool!** 🚀
