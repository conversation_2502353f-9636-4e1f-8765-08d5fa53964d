"""
Vector Database management endpoints
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional
import structlog

from app.services.vector_db import vector_db_manager
from app.utils.vector_db_test import run_comprehensive_test, test_faiss_only, test_pinecone_only

logger = structlog.get_logger()
router = APIRouter()


class VectorDBStatus(BaseModel):
    """Vector database status model"""
    status: str
    db_type: Optional[str] = None
    stats: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class VectorDBTestResults(BaseModel):
    """Vector database test results model"""
    connection: bool
    create_test: bool
    query_test: bool
    stats_test: bool
    db_type: Optional[str] = None
    errors: list


@router.get("/status", response_model=VectorDBStatus)
async def get_vector_db_status():
    """
    Get current vector database status
    """
    try:
        health_check = await vector_db_manager.health_check()
        return VectorDBStatus(**health_check)
    except Exception as e:
        logger.error("Failed to get vector DB status", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get vector database status")


@router.post("/initialize")
async def initialize_vector_db(use_pinecone: bool = False):
    """
    Initialize vector database connection
    """
    try:
        logger.info("Initializing vector database", use_pinecone=use_pinecone)
        
        success = await vector_db_manager.initialize(use_pinecone=use_pinecone)
        
        if success:
            health_check = await vector_db_manager.health_check()
            return {
                "message": "Vector database initialized successfully",
                "db_type": vector_db_manager.db_type,
                "status": health_check
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to initialize vector database")
            
    except Exception as e:
        logger.error("Vector DB initialization failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Vector database initialization failed: {str(e)}")


@router.post("/test/comprehensive")
async def test_vector_db_comprehensive():
    """
    Run comprehensive vector database tests (both FAISS and Pinecone)
    """
    try:
        logger.info("Running comprehensive vector database tests")
        results = await run_comprehensive_test()
        return {
            "message": "Comprehensive vector database tests completed",
            "results": results
        }
    except Exception as e:
        logger.error("Comprehensive vector DB test failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Vector database test failed: {str(e)}")


@router.post("/test/faiss")
async def test_faiss_db():
    """
    Test FAISS vector database specifically
    """
    try:
        logger.info("Testing FAISS vector database")
        results = await test_faiss_only()
        return {
            "message": "FAISS vector database test completed",
            "results": results
        }
    except Exception as e:
        logger.error("FAISS test failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"FAISS test failed: {str(e)}")


@router.post("/test/pinecone")
async def test_pinecone_db():
    """
    Test Pinecone vector database specifically
    """
    try:
        logger.info("Testing Pinecone vector database")
        results = await test_pinecone_only()
        return {
            "message": "Pinecone vector database test completed",
            "results": results
        }
    except Exception as e:
        logger.error("Pinecone test failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Pinecone test failed: {str(e)}")


@router.get("/stats")
async def get_vector_db_stats():
    """
    Get vector database statistics
    """
    try:
        if not vector_db_manager.db:
            raise HTTPException(status_code=400, detail="Vector database not initialized")
        
        stats = await vector_db_manager.db.get_stats()
        return {
            "db_type": vector_db_manager.db_type,
            "stats": stats
        }
    except Exception as e:
        logger.error("Failed to get vector DB stats", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get vector database stats: {str(e)}")


@router.delete("/reset")
async def reset_vector_db():
    """
    Reset vector database (for development/testing)
    """
    try:
        logger.warning("Resetting vector database")
        
        # For FAISS, we can delete the local files
        if vector_db_manager.db_type == "faiss":
            import os
            try:
                if os.path.exists("data/faiss_index.bin"):
                    os.remove("data/faiss_index.bin")
                if os.path.exists("data/faiss_metadata.pkl"):
                    os.remove("data/faiss_metadata.pkl")
                logger.info("FAISS files deleted")
            except Exception as e:
                logger.error("Failed to delete FAISS files", error=str(e))
        
        # Reinitialize
        use_pinecone = vector_db_manager.db_type == "pinecone"
        success = await vector_db_manager.initialize(use_pinecone=use_pinecone)
        
        if success:
            return {
                "message": "Vector database reset successfully",
                "db_type": vector_db_manager.db_type
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to reset vector database")
            
    except Exception as e:
        logger.error("Vector DB reset failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Vector database reset failed: {str(e)}")
