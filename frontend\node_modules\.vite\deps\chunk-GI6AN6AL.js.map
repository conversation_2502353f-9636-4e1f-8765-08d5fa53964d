{"version": 3, "sources": ["../../@mui/material/DefaultPropsProvider/DefaultPropsProvider.js", "../../@mui/material/DefaultPropsProvider/index.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport SystemDefaultPropsProvider, { useDefaultProps as useSystemDefaultProps } from '@mui/system/DefaultPropsProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction DefaultPropsProvider(props) {\n  return /*#__PURE__*/_jsx(SystemDefaultPropsProvider, _extends({}, props));\n}\nprocess.env.NODE_ENV !== \"production\" ? DefaultPropsProvider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  value: PropTypes.object.isRequired\n} : void 0;\nexport default DefaultPropsProvider;\nexport function useDefaultProps(params) {\n  return useSystemDefaultProps(params);\n}", "export { default, useDefaultProps } from './DefaultPropsProvider';"], "mappings": ";;;;;;;;;;;;;;;;;;AAOA,SAAS,qBAAqB,OAAO;AACnC,aAAoB,mBAAAA,KAAK,8BAA4B,SAAS,CAAC,GAAG,KAAK,CAAC;AAC1E;AAgBO,SAASC,iBAAgB,QAAQ;AACtC,SAAO,gBAAsB,MAAM;AACrC;AA3BA,IAGA,OACA,mBAEA;AANA,IAAAC,6BAAA;AAAA;AAAA;AAEA;AACA,YAAuB;AACvB,wBAAsB;AACtB;AACA,yBAA4B;AAI5B,WAAwC,qBAAqB,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQ9F,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,MAIpB,OAAO,kBAAAA,QAAU,OAAO;AAAA,IAC1B,IAAI;AAAA;AAAA;;;ACvBJ,IAAAC,6BAAA;AAAA;AAAA,IAAAA;AAAA;AAAA;", "names": ["_jsx", "useDefaultProps", "init_DefaultPropsProvider", "PropTypes", "init_DefaultPropsProvider"]}