/**
 * API Types for SEC Filing QA Agent
 * Phase 6: Frontend Development
 */

// Query Types
export interface QueryRequest {
  question: string;
  max_chunks?: number;
  model_preference?: 'gemma' | 'deepseek';
  include_sources?: boolean;
  include_confidence?: boolean;
  use_cache?: boolean;
  user_id?: string;
  session_id?: string;
}

export interface QueryResponse {
  success: boolean;
  answer: string;
  sources: Source[];
  confidence: string;
  processing_time: number;
  model_used: string;
  context_chunks: number;
  query_analysis?: QueryAnalysis;
  error?: string;
  suggestions?: string[];
}

export interface Source {
  ticker: string;
  filing_type: string;
  filing_date: string;
  section: string;
  relevance_score: number;
  has_financial_data?: boolean;
  content_preview?: string;
}

export interface QueryAnalysis {
  intent: string;
  tickers: string[];
  filing_types: string[];
  time_references: string[];
  has_comparison: boolean;
  question_type: string;
  complexity_score?: number;
}

// Company Types
export interface Company {
  ticker: string;
  name: string;
  sector: string;
  industry?: string;
  market_cap?: number;
  cik?: string;
  sic?: string;
  website?: string;
  description?: string;
  stats: CompanyStats;
  supported_for_qa: boolean;
  last_updated?: string;
}

export interface CompanyStats {
  total_filings: number;
  filing_types: Record<string, number>;
  latest_filing_date?: string;
  processed_filings: number;
  vector_chunks: number;
}

export interface CompanyListResponse {
  companies: Company[];
  total: number;
  page: number;
  page_size: number;
  has_next: boolean;
  supported_companies: number;
  total_filings: number;
}

// Filing Types
export interface Filing {
  ticker: string;
  filing_type: string;
  filing_date: string;
  accession_number: string;
  url: string;
  title: string;
  size?: number;
  status: FilingProcessingStatus;
  available_for_qa: boolean;
  last_updated?: string;
}

export interface FilingProcessingStatus {
  processed: boolean;
  processing_date?: string;
  chunks_created: number;
  vectors_stored: number;
  sections_parsed: number;
  has_financial_data: boolean;
  processing_time_seconds?: number;
  error_message?: string;
}

export interface FilingListResponse {
  filings: Filing[];
  total: number;
  page: number;
  page_size: number;
  has_next: boolean;
  processed_count: number;
  filing_types: Record<string, number>;
  date_range?: {
    earliest: string;
    latest: string;
  };
}

// Health Check Types
export interface HealthResponse {
  status: string;
  timestamp: string;
  environment: string;
  version: string;
  uptime_seconds: number;
  components: Record<string, ComponentHealth>;
  system_metrics?: SystemMetrics;
}

export interface ComponentHealth {
  status: string;
  message?: string;
  response_time_ms?: number;
  last_check: string;
  details?: Record<string, any>;
}

export interface SystemMetrics {
  cpu_percent: number;
  memory_percent: number;
  disk_percent: number;
  process_count: number;
  load_average: number[];
}

// Batch Processing Types
export interface BatchQueryRequest {
  queries: string[];
  max_chunks?: number;
  model_preference?: string;
  include_sources?: boolean;
  include_confidence?: boolean;
  batch_name?: string;
}

export interface BatchResponse {
  batch_id: string;
  batch_name?: string;
  status: 'processing' | 'completed' | 'failed';
  total_queries: number;
  completed_queries: number;
  successful_queries: number;
  failed_queries: number;
  started_at: string;
  completed_at?: string;
  total_processing_time: number;
  results: BatchQueryResult[];
}

export interface BatchQueryResult {
  query: string;
  success: boolean;
  result?: QueryResponse;
  error?: string;
  processing_time: number;
}

// History Types
export interface QueryHistoryEntry {
  id: string;
  query: string;
  response: QueryResponse;
  user_id: string;
  session_id: string;
  timestamp: string;
  processing_time: number;
  success: boolean;
  model_used?: string;
  sources_count: number;
  confidence?: string;
}

export interface HistoryStats {
  period_days: number;
  total_queries: number;
  successful_queries: number;
  success_rate: number;
  average_processing_time: number;
  most_used_models: Record<string, number>;
  top_query_words: Record<string, number>;
  daily_query_counts: Record<string, number>;
  unique_users: number;
  unique_sessions: number;
}

// Filter Types
export interface QueryFilters {
  companies?: string[];
  filing_types?: string[];
  date_range?: {
    start_date?: string;
    end_date?: string;
  };
  has_financial_data?: boolean;
  min_confidence?: string;
}

// UI State Types
export interface UIState {
  isLoading: boolean;
  error?: string;
  selectedCompanies: string[];
  selectedFilingTypes: string[];
  dateRange: {
    start?: string;
    end?: string;
  };
  showAdvancedFilters: boolean;
  viewMode: 'list' | 'grid' | 'table';
}

// API Error Types
export interface APIError {
  detail: string;
  status_code?: number;
  timestamp?: string;
  errors?: any[];
  message?: string;
}

// Export functionality
export interface ExportRequest {
  format: 'json' | 'csv' | 'xlsx';
  include_sources?: boolean;
  include_metadata?: boolean;
  date_range?: {
    start_date?: string;
    end_date?: string;
  };
  companies?: string[];
  filing_types?: string[];
}

export interface ExportResponse {
  export_id: string;
  status: string;
  format: string;
  started_at: string;
  download_url?: string;
}
