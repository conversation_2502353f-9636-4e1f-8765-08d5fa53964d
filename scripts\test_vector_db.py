#!/usr/bin/env python3
"""
Vector Database Testing Script
"""

import sys
import os
import asyncio

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app.utils.vector_db_test import run_comprehensive_test


def print_test_results(results):
    """Print formatted test results"""
    print("\n" + "="*60)
    print("🔍 VECTOR DATABASE TEST RESULTS")
    print("="*60)
    
    # FAISS Results
    print(f"\n📁 FAISS Database Test:")
    faiss_results = results.get('faiss_test', {})
    print(f"  Connection: {'✅ PASS' if faiss_results.get('connection') else '❌ FAIL'}")
    print(f"  Storage:    {'✅ PASS' if faiss_results.get('create_test') else '❌ FAIL'}")
    print(f"  Query:      {'✅ PASS' if faiss_results.get('query_test') else '❌ FAIL'}")
    print(f"  Stats:      {'✅ PASS' if faiss_results.get('stats_test') else '❌ FAIL'}")
    
    if faiss_results.get('errors'):
        print(f"  Errors: {', '.join(faiss_results['errors'])}")
    
    # Pinecone Results
    print(f"\n☁️  Pinecone Database Test:")
    pinecone_results = results.get('pinecone_test', {})
    print(f"  Connection: {'✅ PASS' if pinecone_results.get('connection') else '❌ FAIL'}")
    
    if pinecone_results.get('connection'):
        print(f"  Storage:    {'✅ PASS' if pinecone_results.get('create_test') else '❌ FAIL'}")
        print(f"  Query:      {'✅ PASS' if pinecone_results.get('query_test') else '❌ FAIL'}")
        print(f"  Stats:      {'✅ PASS' if pinecone_results.get('stats_test') else '❌ FAIL'}")
    
    if pinecone_results.get('errors'):
        print(f"  Errors: {', '.join(pinecone_results['errors'])}")
    
    # Summary
    summary = results.get('summary', {})
    print(f"\n📊 SUMMARY:")
    print(f"  FAISS Working:    {'✅ YES' if summary.get('faiss_working') else '❌ NO'}")
    print(f"  Pinecone Working: {'✅ YES' if summary.get('pinecone_working') else '❌ NO'}")
    print(f"  Recommended DB:   🎯 {summary.get('recommended_db', 'unknown').upper()}")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if summary.get('pinecone_working'):
        print("  • Pinecone is working - recommended for production")
        print("  • Configure your .env file with valid Pinecone API keys")
    elif summary.get('faiss_working'):
        print("  • FAISS is working - good for development and testing")
        print("  • Consider setting up Pinecone for production use")
    else:
        print("  • ⚠️  No vector databases are working")
        print("  • Check your dependencies and configuration")
    
    print("\n" + "="*60)


async def main():
    """Main test function"""
    print("🚀 Starting Vector Database Tests...")
    print("This will test both FAISS (local) and Pinecone (cloud) databases.")
    
    try:
        # Run comprehensive tests
        results = await run_comprehensive_test()
        
        # Print results
        print_test_results(results)
        
        # Exit with appropriate code
        summary = results.get('summary', {})
        if summary.get('faiss_working') or summary.get('pinecone_working'):
            print("✅ At least one vector database is working!")
            sys.exit(0)
        else:
            print("❌ No vector databases are working!")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    # Check if we're in the right directory
    if not os.path.exists("backend/app"):
        print("❌ Please run this script from the project root directory")
        print("   Example: python scripts/test_vector_db.py")
        sys.exit(1)
    
    # Run the tests
    asyncio.run(main())
