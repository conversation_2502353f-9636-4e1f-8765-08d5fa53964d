"""
Vector Storage Service
Handles vector storage and retrieval with Pinecone primary and FAISS local fallback
"""

import os
import json
import pickle
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
import structlog
from datetime import datetime
import asyncio
from pathlib import Path

# Pinecone imports
try:
    import pinecone
    from pinecone import Pinecone, ServerlessSpec
    PINECONE_AVAILABLE = True
except ImportError:
    PINECONE_AVAILABLE = False
    pinecone = None

# FAISS imports
try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    faiss = None

from app.core.config import settings

logger = structlog.get_logger()


class VectorStorage:
    """
    Vector storage service with Pinecone primary and FAISS local fallback
    """
    
    def __init__(self):
        self.pinecone_client: Optional[Pinecone] = None
        self.pinecone_index = None
        self.faiss_index = None
        self.faiss_metadata: Dict[int, Dict[str, Any]] = {}
        self.faiss_id_counter = 0
        
        # Storage paths for FAISS
        self.storage_dir = Path("backend/data/vector_storage")
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        self.faiss_index_path = self.storage_dir / "faiss_index.bin"
        self.faiss_metadata_path = self.storage_dir / "faiss_metadata.json"
        
        # Configuration
        self.index_name = settings.PINECONE_INDEX_NAME
        self.dimension = 384  # Will be updated based on embedding service
        
        # Fallback tracking
        self.pinecone_failures = 0
        self.fallback_active = False
        self.last_pinecone_error: Optional[str] = None
        self.fallback_start_time: Optional[datetime] = None
        
        # Initialize services
        self._initialize_pinecone()
        self._initialize_faiss()
    
    def _initialize_pinecone(self):
        """Initialize Pinecone client and index"""
        if not PINECONE_AVAILABLE:
            logger.warning("Pinecone not available, using FAISS fallback only")
            self.fallback_active = True
            return
        
        try:
            if settings.PINECONE_API_KEY:
                self.pinecone_client = Pinecone(api_key=settings.PINECONE_API_KEY)
                logger.info("Pinecone client initialized")
                
                # Check if index exists, create if not
                self._ensure_pinecone_index()
                
            else:
                logger.warning("Pinecone API key not found, using FAISS fallback only")
                self.fallback_active = True
                
        except Exception as e:
            logger.error("Failed to initialize Pinecone", error=str(e))
            self._activate_fallback(f"Pinecone initialization failed: {str(e)}")
    
    def _ensure_pinecone_index(self):
        """Ensure Pinecone index exists"""
        try:
            # List existing indexes
            existing_indexes = self.pinecone_client.list_indexes()
            index_names = [idx.name for idx in existing_indexes.indexes]
            
            if self.index_name not in index_names:
                logger.info("Creating Pinecone index", index_name=self.index_name, dimension=self.dimension)
                
                # Create index with serverless spec
                self.pinecone_client.create_index(
                    name=self.index_name,
                    dimension=self.dimension,
                    metric="cosine",
                    spec=ServerlessSpec(
                        cloud="aws",
                        region="us-east-1"
                    )
                )
                
                # Wait for index to be ready
                import time
                time.sleep(10)
            
            # Connect to index
            self.pinecone_index = self.pinecone_client.Index(self.index_name)
            logger.info("Connected to Pinecone index", index_name=self.index_name)
            
        except Exception as e:
            logger.error("Failed to ensure Pinecone index", error=str(e))
            self._activate_fallback(f"Pinecone index setup failed: {str(e)}")
    
    def _initialize_faiss(self):
        """Initialize FAISS index"""
        if not FAISS_AVAILABLE:
            logger.error("FAISS not available - no fallback possible")
            raise Exception("FAISS not available")
        
        try:
            # Try to load existing index
            if self.faiss_index_path.exists() and self.faiss_metadata_path.exists():
                self.faiss_index = faiss.read_index(str(self.faiss_index_path))
                
                with open(self.faiss_metadata_path, 'r') as f:
                    metadata_data = json.load(f)
                    self.faiss_metadata = {int(k): v for k, v in metadata_data['metadata'].items()}
                    self.faiss_id_counter = metadata_data.get('id_counter', 0)
                
                logger.info("Loaded existing FAISS index", 
                           vectors_count=self.faiss_index.ntotal,
                           dimension=self.faiss_index.d)
            else:
                # Create new index
                self.faiss_index = faiss.IndexFlatIP(self.dimension)  # Inner product (cosine similarity)
                logger.info("Created new FAISS index", dimension=self.dimension)
                
        except Exception as e:
            logger.error("Failed to initialize FAISS", error=str(e))
            raise
    
    def update_dimension(self, new_dimension: int):
        """Update vector dimension (call this after embedding service is initialized)"""
        if new_dimension != self.dimension:
            logger.info("Updating vector dimension", old_dim=self.dimension, new_dim=new_dimension)
            self.dimension = new_dimension
            
            # Reinitialize FAISS with new dimension if no vectors stored yet
            if self.faiss_index.ntotal == 0:
                self.faiss_index = faiss.IndexFlatIP(self.dimension)
                logger.info("Reinitialized FAISS index with new dimension")
    
    async def store_vectors(self, vectors: List[List[float]], metadata_list: List[Dict[str, Any]]) -> List[str]:
        """
        Store vectors with metadata
        
        Args:
            vectors: List of embedding vectors
            metadata_list: List of metadata dictionaries for each vector
            
        Returns:
            List of vector IDs
        """
        if len(vectors) != len(metadata_list):
            raise ValueError("Number of vectors must match number of metadata entries")
        
        if not vectors:
            return []
        
        logger.info("Storing vectors", 
                   count=len(vectors), 
                   dimension=len(vectors[0]) if vectors else 0,
                   fallback_active=self.fallback_active)
        
        try:
            # Try Pinecone first (if not in fallback mode)
            if not self.fallback_active and self.pinecone_index:
                return await self._store_vectors_pinecone(vectors, metadata_list)
            else:
                # Use FAISS fallback
                return await self._store_vectors_faiss(vectors, metadata_list)
                
        except Exception as e:
            logger.error("Vector storage failed", error=str(e))
            
            # If Pinecone failed, try FAISS fallback
            if not self.fallback_active:
                logger.warning("Switching to FAISS fallback for vector storage")
                self._activate_fallback(str(e))
                return await self._store_vectors_faiss(vectors, metadata_list)
            else:
                raise
    
    async def _store_vectors_pinecone(self, vectors: List[List[float]], metadata_list: List[Dict[str, Any]]) -> List[str]:
        """Store vectors in Pinecone"""
        if not self.pinecone_index:
            raise Exception("Pinecone index not available")
        
        # Prepare vectors for Pinecone
        vector_data = []
        ids = []
        
        for i, (vector, metadata) in enumerate(zip(vectors, metadata_list)):
            vector_id = f"{metadata.get('ticker', 'unknown')}_{metadata.get('accession_number', 'unknown')}_{i}_{int(datetime.now().timestamp())}"
            ids.append(vector_id)
            
            vector_data.append({
                "id": vector_id,
                "values": vector,
                "metadata": metadata
            })
        
        # Store in batches
        batch_size = 100
        for i in range(0, len(vector_data), batch_size):
            batch = vector_data[i:i + batch_size]
            self.pinecone_index.upsert(vectors=batch)
        
        logger.info("Vectors stored in Pinecone", count=len(ids))
        return ids
    
    async def _store_vectors_faiss(self, vectors: List[List[float]], metadata_list: List[Dict[str, Any]]) -> List[str]:
        """Store vectors in FAISS"""
        if not self.faiss_index:
            raise Exception("FAISS index not available")
        
        # Convert to numpy array
        vector_array = np.array(vectors, dtype=np.float32)
        
        # Normalize vectors for cosine similarity
        faiss.normalize_L2(vector_array)
        
        # Add to index
        start_id = self.faiss_id_counter
        self.faiss_index.add(vector_array)
        
        # Store metadata
        ids = []
        for i, metadata in enumerate(metadata_list):
            vector_id = f"faiss_{start_id + i}"
            ids.append(vector_id)
            self.faiss_metadata[start_id + i] = metadata
        
        self.faiss_id_counter += len(vectors)
        
        # Save to disk
        await self._save_faiss_index()
        
        logger.info("Vectors stored in FAISS", count=len(ids))
        return ids
    
    async def search_vectors(self, query_vector: List[float], top_k: int = 10, 
                           filter_metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Search for similar vectors
        
        Args:
            query_vector: Query embedding vector
            top_k: Number of results to return
            filter_metadata: Optional metadata filters
            
        Returns:
            List of search results with scores and metadata
        """
        logger.info("Searching vectors", 
                   top_k=top_k, 
                   has_filters=filter_metadata is not None,
                   fallback_active=self.fallback_active)
        
        try:
            # Try Pinecone first (if not in fallback mode)
            if not self.fallback_active and self.pinecone_index:
                return await self._search_vectors_pinecone(query_vector, top_k, filter_metadata)
            else:
                # Use FAISS fallback
                return await self._search_vectors_faiss(query_vector, top_k, filter_metadata)
                
        except Exception as e:
            logger.error("Vector search failed", error=str(e))
            
            # If Pinecone failed, try FAISS fallback
            if not self.fallback_active:
                logger.warning("Switching to FAISS fallback for vector search")
                self._activate_fallback(str(e))
                return await self._search_vectors_faiss(query_vector, top_k, filter_metadata)
            else:
                raise
    
    async def _search_vectors_pinecone(self, query_vector: List[float], top_k: int, 
                                     filter_metadata: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Search vectors in Pinecone"""
        if not self.pinecone_index:
            raise Exception("Pinecone index not available")
        
        # Prepare query
        query_params = {
            "vector": query_vector,
            "top_k": top_k,
            "include_metadata": True
        }
        
        if filter_metadata:
            query_params["filter"] = filter_metadata
        
        # Execute search
        results = self.pinecone_index.query(**query_params)
        
        # Format results
        formatted_results = []
        for match in results.matches:
            formatted_results.append({
                "id": match.id,
                "score": match.score,
                "metadata": match.metadata
            })
        
        logger.info("Pinecone search completed", results_count=len(formatted_results))
        return formatted_results

    async def _search_vectors_faiss(self, query_vector: List[float], top_k: int,
                                  filter_metadata: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Search vectors in FAISS"""
        if not self.faiss_index:
            raise Exception("FAISS index not available")

        if self.faiss_index.ntotal == 0:
            logger.info("FAISS index is empty")
            return []

        # Convert query to numpy array and normalize
        query_array = np.array([query_vector], dtype=np.float32)
        faiss.normalize_L2(query_array)

        # Search
        scores, indices = self.faiss_index.search(query_array, min(top_k, self.faiss_index.ntotal))

        # Format results
        formatted_results = []
        for score, idx in zip(scores[0], indices[0]):
            if idx == -1:  # FAISS returns -1 for invalid indices
                continue

            metadata = self.faiss_metadata.get(idx, {})

            # Apply metadata filters if provided
            if filter_metadata:
                if not self._matches_filter(metadata, filter_metadata):
                    continue

            formatted_results.append({
                "id": f"faiss_{idx}",
                "score": float(score),
                "metadata": metadata
            })

        logger.info("FAISS search completed", results_count=len(formatted_results))
        return formatted_results

    def _matches_filter(self, metadata: Dict[str, Any], filter_metadata: Dict[str, Any]) -> bool:
        """Check if metadata matches filter criteria"""
        for key, value in filter_metadata.items():
            if key not in metadata:
                return False
            if metadata[key] != value:
                return False
        return True

    async def _save_faiss_index(self):
        """Save FAISS index and metadata to disk"""
        try:
            # Save index
            faiss.write_index(self.faiss_index, str(self.faiss_index_path))

            # Save metadata
            metadata_data = {
                "metadata": {str(k): v for k, v in self.faiss_metadata.items()},
                "id_counter": self.faiss_id_counter,
                "saved_at": datetime.now().isoformat()
            }

            with open(self.faiss_metadata_path, 'w') as f:
                json.dump(metadata_data, f, indent=2)

            logger.debug("FAISS index saved to disk")

        except Exception as e:
            logger.error("Failed to save FAISS index", error=str(e))

    def _activate_fallback(self, reason: str):
        """Activate fallback mode"""
        self.fallback_active = True
        self.pinecone_failures += 1
        self.last_pinecone_error = reason
        self.fallback_start_time = datetime.now()

        logger.warning("Vector storage fallback activated",
                      reason=reason,
                      failure_count=self.pinecone_failures,
                      fallback_start_time=self.fallback_start_time)

    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics"""
        stats = {
            "primary_service": "pinecone",
            "fallback_service": "faiss",
            "current_service": "faiss" if self.fallback_active else "pinecone",
            "fallback_active": self.fallback_active,
            "pinecone_failures": self.pinecone_failures,
            "last_error": self.last_pinecone_error,
            "fallback_start_time": self.fallback_start_time.isoformat() if self.fallback_start_time else None,
            "dimension": self.dimension
        }

        # Add service-specific stats
        if self.fallback_active or not self.pinecone_index:
            # FAISS stats
            stats.update({
                "faiss_vectors_count": self.faiss_index.ntotal if self.faiss_index else 0,
                "faiss_metadata_count": len(self.faiss_metadata),
                "faiss_index_path": str(self.faiss_index_path),
                "faiss_metadata_path": str(self.faiss_metadata_path)
            })
        else:
            # Pinecone stats
            try:
                index_stats = self.pinecone_index.describe_index_stats()
                stats.update({
                    "pinecone_vectors_count": index_stats.total_vector_count,
                    "pinecone_index_name": self.index_name
                })
            except Exception as e:
                stats["pinecone_stats_error"] = str(e)

        return stats

    async def delete_vectors(self, vector_ids: List[str]) -> bool:
        """Delete vectors by IDs"""
        if not vector_ids:
            return True

        logger.info("Deleting vectors", count=len(vector_ids), fallback_active=self.fallback_active)

        try:
            if not self.fallback_active and self.pinecone_index:
                # Delete from Pinecone
                self.pinecone_index.delete(ids=vector_ids)
                logger.info("Vectors deleted from Pinecone", count=len(vector_ids))
            else:
                # Delete from FAISS (more complex as FAISS doesn't support deletion)
                logger.warning("FAISS doesn't support vector deletion - consider rebuilding index")

            return True

        except Exception as e:
            logger.error("Vector deletion failed", error=str(e))
            return False

    async def clear_all_vectors(self) -> bool:
        """Clear all vectors from storage"""
        logger.warning("Clearing all vectors from storage")

        try:
            if not self.fallback_active and self.pinecone_index:
                # Clear Pinecone index
                self.pinecone_index.delete(delete_all=True)
                logger.info("All vectors cleared from Pinecone")
            else:
                # Clear FAISS
                self.faiss_index = faiss.IndexFlatIP(self.dimension)
                self.faiss_metadata = {}
                self.faiss_id_counter = 0
                await self._save_faiss_index()
                logger.info("All vectors cleared from FAISS")

            return True

        except Exception as e:
            logger.error("Failed to clear all vectors", error=str(e))
            return False


# Global vector storage instance
vector_storage = VectorStorage()
