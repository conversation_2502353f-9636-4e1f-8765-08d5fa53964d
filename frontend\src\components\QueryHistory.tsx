/**
 * Query History Component
 * Phase 6: Frontend Development - Task 6.3
 */

import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  InputAdornment,
  Collapse,
  Divider,
  Tooltip,
  Menu,
  MenuItem,
} from '@mui/material';
import {
  History as HistoryIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  Delete as DeleteIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  MoreVert as MoreVertIcon,
  FilterList as FilterIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';
import { useQueryHistory, useBookmarks } from '../contexts/AppContext';

interface QueryHistoryProps {
  onQuerySelect?: (query: string) => void;
  maxItems?: number;
  showSearch?: boolean;
  showBookmarks?: boolean;
  compact?: boolean;
}

const QueryHistory: React.FC<QueryHistoryProps> = ({
  onQuerySelect,
  maxItems = 50,
  showSearch = true,
  showBookmarks = true,
  compact = false,
}) => {
  const { queryHistory, clearHistory } = useQueryHistory();
  const { isBookmarked, addBookmark, removeBookmark } = useBookmarks();
  
  // Local state
  const [searchTerm, setSearchTerm] = useState('');
  const [showClearDialog, setShowClearDialog] = useState(false);
  const [expanded, setExpanded] = useState(!compact);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedQuery, setSelectedQuery] = useState<string>('');

  // Filter queries based on search term
  const filteredQueries = queryHistory
    .filter(query => 
      query.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .slice(0, maxItems);

  // Handle query selection
  const handleQuerySelect = (query: string) => {
    if (onQuerySelect) {
      onQuerySelect(query);
    }
  };

  // Handle bookmark toggle
  const handleBookmarkToggle = (query: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (isBookmarked(query)) {
      removeBookmark(query);
    } else {
      addBookmark(query);
    }
  };

  // Handle menu actions
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, query: string) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedQuery(query);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedQuery('');
  };

  // Handle clear history
  const handleClearHistory = () => {
    clearHistory();
    setShowClearDialog(false);
  };

  if (queryHistory.length === 0) {
    return (
      <Paper elevation={2} sx={{ p: 3, textAlign: 'center' }}>
        <HistoryIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No Query History
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Your recent queries will appear here
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper elevation={2} sx={{ mb: 3 }}>
      {/* Header */}
      <Box
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        p={2}
        sx={{ cursor: compact ? 'pointer' : 'default' }}
        onClick={compact ? () => setExpanded(!expanded) : undefined}
      >
        <Box display="flex" alignItems="center" gap={1}>
          <HistoryIcon color="primary" />
          <Typography variant="h6">
            Query History
          </Typography>
          <Chip
            label={queryHistory.length}
            size="small"
            color="primary"
            variant="outlined"
          />
        </Box>

        <Box display="flex" alignItems="center" gap={1}>
          {queryHistory.length > 0 && (
            <Button
              size="small"
              color="error"
              onClick={() => setShowClearDialog(true)}
              startIcon={<ClearIcon />}
            >
              Clear All
            </Button>
          )}
          
          {compact && (
            <IconButton size="small">
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          )}
        </Box>
      </Box>

      <Collapse in={expanded}>
        <Box px={2} pb={2}>
          {/* Search */}
          {showSearch && (
            <TextField
              fullWidth
              size="small"
              placeholder="Search query history..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: searchTerm && (
                  <InputAdornment position="end">
                    <IconButton
                      size="small"
                      onClick={() => setSearchTerm('')}
                    >
                      <ClearIcon />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 2 }}
            />
          )}

          {/* Query List */}
          <List dense>
            {filteredQueries.map((query, index) => (
              <React.Fragment key={`${query}-${index}`}>
                <ListItem
                  button
                  onClick={() => handleQuerySelect(query)}
                  sx={{
                    borderRadius: 1,
                    mb: 1,
                    '&:hover': {
                      bgcolor: 'action.hover',
                    },
                  }}
                >
                  <ListItemText
                    primary={
                      <Typography
                        variant="body2"
                        sx={{
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                        }}
                      >
                        {query}
                      </Typography>
                    }
                    secondary={
                      <Box display="flex" alignItems="center" gap={1} mt={0.5}>
                        <Typography variant="caption" color="text.secondary">
                          {formatDistanceToNow(new Date(Date.now() - index * 60000), { addSuffix: true })}
                        </Typography>
                        
                        {isBookmarked(query) && (
                          <Chip
                            icon={<BookmarkIcon />}
                            label="Bookmarked"
                            size="small"
                            color="primary"
                            variant="outlined"
                            sx={{ height: 20 }}
                          />
                        )}
                      </Box>
                    }
                  />
                  
                  <ListItemSecondaryAction>
                    <Box display="flex" alignItems="center">
                      {showBookmarks && (
                        <Tooltip title={isBookmarked(query) ? 'Remove bookmark' : 'Add bookmark'}>
                          <IconButton
                            size="small"
                            onClick={(e) => handleBookmarkToggle(query, e)}
                            color={isBookmarked(query) ? 'primary' : 'default'}
                          >
                            {isBookmarked(query) ? <BookmarkIcon /> : <BookmarkBorderIcon />}
                          </IconButton>
                        </Tooltip>
                      )}
                      
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuOpen(e, query)}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </Box>
                  </ListItemSecondaryAction>
                </ListItem>
                
                {index < filteredQueries.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>

          {/* Show more indicator */}
          {queryHistory.length > maxItems && (
            <Box textAlign="center" mt={2}>
              <Typography variant="caption" color="text.secondary">
                Showing {Math.min(maxItems, filteredQueries.length)} of {queryHistory.length} queries
              </Typography>
            </Box>
          )}
        </Box>
      </Collapse>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem
          onClick={() => {
            handleQuerySelect(selectedQuery);
            handleMenuClose();
          }}
        >
          <SearchIcon sx={{ mr: 1 }} />
          Run Query
        </MenuItem>
        
        <MenuItem
          onClick={() => {
            if (isBookmarked(selectedQuery)) {
              removeBookmark(selectedQuery);
            } else {
              addBookmark(selectedQuery);
            }
            handleMenuClose();
          }}
        >
          {isBookmarked(selectedQuery) ? (
            <>
              <BookmarkIcon sx={{ mr: 1 }} />
              Remove Bookmark
            </>
          ) : (
            <>
              <BookmarkBorderIcon sx={{ mr: 1 }} />
              Add Bookmark
            </>
          )}
        </MenuItem>
        
        <MenuItem
          onClick={() => {
            navigator.clipboard.writeText(selectedQuery);
            handleMenuClose();
          }}
        >
          <Typography sx={{ mr: 1 }}>📋</Typography>
          Copy Query
        </MenuItem>
      </Menu>

      {/* Clear History Dialog */}
      <Dialog
        open={showClearDialog}
        onClose={() => setShowClearDialog(false)}
      >
        <DialogTitle>Clear Query History</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to clear all query history? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowClearDialog(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleClearHistory}
            color="error"
            variant="contained"
          >
            Clear All
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default QueryHistory;
