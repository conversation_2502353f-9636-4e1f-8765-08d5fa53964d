"""
SEC filings management endpoints - Phase 5 Enhanced
"""

from fastapi import APIRouter, Query, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
import structlog

logger = structlog.get_logger()
router = APIRouter()


class FilingProcessingStatus(BaseModel):
    """Filing processing status model"""
    processed: bool = Field(default=False, description="Whether filing is processed")
    processing_date: Optional[str] = Field(default=None, description="When processing completed")
    chunks_created: int = Field(default=0, description="Number of text chunks created")
    vectors_stored: int = Field(default=0, description="Number of vectors stored")
    sections_parsed: int = Field(default=0, description="Number of sections parsed")
    has_financial_data: bool = Field(default=False, description="Contains financial data")
    processing_time_seconds: Optional[float] = Field(default=None, description="Processing duration")
    error_message: Optional[str] = Field(default=None, description="Processing error if any")


class Filing(BaseModel):
    """Enhanced SEC filing model"""
    ticker: str = Field(..., description="Company ticker symbol")
    filing_type: str = Field(..., description="Filing type (10-K, 10-Q, etc.)")
    filing_date: str = Field(..., description="Filing date (YYYY-MM-DD)")
    period_end_date: Optional[str] = Field(default=None, description="Period end date")
    accession_number: str = Field(..., description="SEC accession number")
    url: str = Field(..., description="SEC filing URL")
    title: str = Field(..., description="Filing title")
    size: Optional[int] = Field(default=None, description="File size in bytes")
    document_count: Optional[int] = Field(default=None, description="Number of documents")
    cik: Optional[str] = Field(default=None, description="Central Index Key")
    status: FilingProcessingStatus = Field(default_factory=FilingProcessingStatus, description="Processing status")
    available_for_qa: bool = Field(default=False, description="Available for Q&A queries")
    last_updated: Optional[str] = Field(default=None, description="Last update timestamp")


class FilingList(BaseModel):
    """Enhanced filing list response"""
    filings: List[Filing]
    total: int
    page: int = Field(default=1, description="Current page number")
    page_size: int = Field(default=20, description="Items per page")
    has_next: bool = Field(default=False, description="Whether there are more pages")
    processed_count: int = Field(default=0, description="Number of processed filings")
    filing_types: Dict[str, int] = Field(default_factory=dict, description="Count by filing type")
    date_range: Optional[Dict[str, str]] = Field(default=None, description="Date range of results")


class FilingProcessRequest(BaseModel):
    """Filing processing request model"""
    ticker: str = Field(..., description="Company ticker")
    accession_number: str = Field(..., description="SEC accession number")
    priority: Optional[str] = Field(default="normal", description="Processing priority")
    force_reprocess: bool = Field(default=False, description="Force reprocessing if already done")


class FilingSearchRequest(BaseModel):
    """Advanced filing search request"""
    tickers: Optional[List[str]] = Field(default=None, description="Filter by tickers")
    filing_types: Optional[List[str]] = Field(default=None, description="Filter by filing types")
    start_date: Optional[str] = Field(default=None, description="Start date (YYYY-MM-DD)")
    end_date: Optional[str] = Field(default=None, description="End date (YYYY-MM-DD)")
    processed_only: Optional[bool] = Field(default=None, description="Only processed filings")
    has_financial_data: Optional[bool] = Field(default=None, description="Has financial data")
    min_size: Optional[int] = Field(default=None, description="Minimum file size")
    search_text: Optional[str] = Field(default=None, description="Search in title/content")


@router.get("/", response_model=FilingList)
async def get_filings(
    ticker: Optional[str] = Query(None, description="Filter by ticker symbol"),
    filing_type: Optional[str] = Query(None, description="Filter by filing type (10-K, 10-Q, 8-K, etc.)"),
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    processed_only: Optional[bool] = Query(None, description="Only show processed filings"),
    has_financial_data: Optional[bool] = Query(None, description="Filter by financial data presence"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size")
):
    """
    Get list of SEC filings with enhanced filtering and processing status

    This endpoint provides comprehensive filing information including:
    - Basic filing details (ticker, type, date, URL)
    - Processing status and statistics
    - Q&A availability status
    - Advanced filtering options
    """
    try:
        # Get filings from vector storage (processed filings)
        from app.services.vector_storage import vector_storage

        filings = await _get_filings_from_storage(
            ticker, filing_type, start_date, end_date,
            processed_only, has_financial_data
        )

        # Apply pagination
        total_filings = len(filings)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_filings = filings[start_idx:end_idx]

        # Calculate statistics
        processed_count = sum(1 for f in filings if f.status.processed)
        filing_types_count = {}
        date_range = {"earliest": None, "latest": None}

        for filing in filings:
            # Count filing types
            ft = filing.filing_type
            filing_types_count[ft] = filing_types_count.get(ft, 0) + 1

            # Track date range
            filing_date = filing.filing_date
            if not date_range["earliest"] or filing_date < date_range["earliest"]:
                date_range["earliest"] = filing_date
            if not date_range["latest"] or filing_date > date_range["latest"]:
                date_range["latest"] = filing_date

        logger.info("Retrieved filings list",
                   total=total_filings,
                   processed=processed_count,
                   page=page,
                   page_size=page_size)

        return FilingList(
            filings=paginated_filings,
            total=total_filings,
            page=page,
            page_size=page_size,
            has_next=end_idx < total_filings,
            processed_count=processed_count,
            filing_types=filing_types_count,
            date_range=date_range if date_range["earliest"] else None
        )

    except Exception as e:
        logger.error("Failed to get filings", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve filings")


async def _get_filings_from_storage(ticker: Optional[str], filing_type: Optional[str],
                                   start_date: Optional[str], end_date: Optional[str],
                                   processed_only: Optional[bool],
                                   has_financial_data: Optional[bool]) -> List[Filing]:
    """Get filings from vector storage with filtering"""
    try:
        from app.services.vector_storage import vector_storage

        # Get all metadata
        metadata = vector_storage.faiss_metadata or {}

        # Group chunks by filing
        filings_map = {}
        for chunk in metadata.values():
            accession = chunk.get("accession_number", "unknown")
            if accession not in filings_map:
                filings_map[accession] = {
                    "chunks": [],
                    "ticker": chunk.get("ticker"),
                    "filing_type": chunk.get("filing_type"),
                    "filing_date": chunk.get("filing_date"),
                    "accession_number": accession
                }
            filings_map[accession]["chunks"].append(chunk)

        # Convert to Filing objects
        filings = []
        for accession, filing_data in filings_map.items():
            chunks = filing_data["chunks"]

            # Calculate processing status
            status = FilingProcessingStatus(
                processed=True,  # If in vector storage, it's processed
                processing_date=chunks[0].get("processed_at") if chunks else None,
                chunks_created=len(chunks),
                vectors_stored=len(chunks),
                sections_parsed=len(set(chunk.get("section_name") for chunk in chunks)),
                has_financial_data=any(chunk.get("has_financial_data", False) for chunk in chunks),
                processing_time_seconds=None  # Not stored currently
            )

            filing = Filing(
                ticker=filing_data["ticker"] or "Unknown",
                filing_type=filing_data["filing_type"] or "Unknown",
                filing_date=filing_data["filing_date"] or "Unknown",
                accession_number=accession,
                url=f"https://www.sec.gov/Archives/edgar/data/{accession}",
                title=f"{filing_data['filing_type']} Filing",
                size=sum(chunk.get("chunk_size", 0) for chunk in chunks),
                document_count=1,
                status=status,
                available_for_qa=True,
                last_updated=datetime.now().isoformat()
            )

            # Apply filters
            if ticker and filing.ticker.upper() != ticker.upper():
                continue
            if filing_type and filing.filing_type.upper() != filing_type.upper():
                continue
            if start_date and filing.filing_date < start_date:
                continue
            if end_date and filing.filing_date > end_date:
                continue
            if processed_only is not None and filing.status.processed != processed_only:
                continue
            if has_financial_data is not None and filing.status.has_financial_data != has_financial_data:
                continue

            filings.append(filing)

        # Sort by filing date (newest first)
        filings.sort(key=lambda x: x.filing_date, reverse=True)

        return filings

    except Exception as e:
        logger.error("Failed to get filings from storage", error=str(e))
        return []


@router.get("/{ticker}/{filing_type}")
async def get_company_filings(
    ticker: str,
    filing_type: str,
    limit: int = Query(10, ge=1, le=50, description="Number of filings to return")
):
    """
    Get recent filings for a specific company and filing type

    Returns the most recent filings of the specified type for the company,
    including processing status and Q&A availability.
    """
    ticker = ticker.upper()
    filing_type = filing_type.upper()

    try:
        filings = await _get_filings_from_storage(
            ticker=ticker,
            filing_type=filing_type,
            start_date=None,
            end_date=None,
            processed_only=None,
            has_financial_data=None
        )

        # Limit results
        limited_filings = filings[:limit]

        logger.info("Retrieved company filings by type",
                   ticker=ticker,
                   filing_type=filing_type,
                   count=len(limited_filings))

        return {
            "ticker": ticker,
            "filing_type": filing_type,
            "total_filings": len(filings),
            "filings": [filing.dict() for filing in limited_filings],
            "all_available_for_qa": all(f.available_for_qa for f in limited_filings)
        }

    except Exception as e:
        logger.error("Failed to get company filings by type",
                    ticker=ticker, filing_type=filing_type, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve company filings")


@router.get("/{ticker}/{accession_number}/content")
async def get_filing_content(ticker: str, accession_number: str, document_name: str):
    """
    Get the content of a specific filing document
    """
    try:
        from app.services.sec_api import filing_manager

        content = await filing_manager.get_filing_content(
            ticker=ticker.upper(),
            accession_number=accession_number,
            document_name=document_name
        )

        return {
            "ticker": ticker.upper(),
            "accession_number": accession_number,
            "document_name": document_name,
            "content": content,
            "content_length": len(content)
        }

    except Exception as e:
        logger.error("Failed to get filing content",
                    ticker=ticker, accession_number=accession_number, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve filing content")


@router.post("/process/{ticker}/{filing_type}")
async def process_filing(ticker: str, filing_type: str, filing_date: str):
    """
    Trigger processing of a specific filing
    """
    # TODO: Implement filing processing pipeline
    logger.info("Filing processing requested",
                ticker=ticker, filing_type=filing_type, filing_date=filing_date)

    return {
        "message": "Filing processing will be implemented in Phase 2",
        "ticker": ticker.upper(),
        "filing_type": filing_type.upper(),
        "filing_date": filing_date,
        "status": "queued"
    }
