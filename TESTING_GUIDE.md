# 🧪 Complete Testing Guide: Frontend + Backend Integration

## 🚀 **Quick Start Testing**

### **Step 1: Start Both Servers**

**Terminal 1 - Backend:**
```bash
cd backend
python -m uvicorn app.main:app --reload
```
*Backend will run on: http://127.0.0.1:8000*

**Terminal 2 - Frontend:**
```bash
cd frontend
npm run dev
```
*Frontend will run on: http://localhost:5173*

### **Step 2: Run Integration Tests**

**Terminal 3 - Integration Test:**
```bash
python test_integration_complete.py
```

## 📋 **Manual Testing Checklist**

### **🔍 Backend API Testing**

1. **Health Check**
   - Visit: http://127.0.0.1:8000/api/v1/health/
   - Should show: `{"status": "healthy", ...}`

2. **API Documentation**
   - Visit: http://127.0.0.1:8000/docs
   - Interactive Swagger UI should load

3. **Companies Endpoint**
   - Visit: http://127.0.0.1:8000/api/v1/companies/
   - Should show list of companies with Q&A support

4. **Test Query (via API docs)**
   - Go to: http://127.0.0.1:8000/docs
   - Find `POST /api/v1/query/`
   - Click "Try it out"
   - Use this test query:
   ```json
   {
     "question": "What are Apple's main revenue sources?",
     "max_chunks": 3,
     "model_preference": "gemma",
     "include_sources": true,
     "include_confidence": true
   }
   ```

### **🌐 Frontend Testing**

1. **Basic UI Load**
   - Visit: http://localhost:5173
   - Should see "SEC Filing QA Agent" title
   - Query interface should be visible

2. **Query Interface Testing**
   - Type a question in the text area
   - Try: "What are Apple's primary revenue drivers?"
   - Click "Ask Question" button
   - Should show loading state, then results

3. **Advanced Settings**
   - Click "Advanced Settings"
   - Try different model preferences
   - Toggle switches (sources, confidence, cache)

4. **Mobile Responsiveness**
   - Resize browser window to mobile size
   - Should see floating action buttons (FABs)
   - Test mobile navigation

5. **Sample Queries**
   - Click on sample query chips
   - Should populate the query input
   - Submit and verify results

### **🔧 Advanced Features Testing**

1. **Query History**
   - Make several queries
   - Check if history is saved
   - Search through history

2. **Bookmarks**
   - Bookmark some queries
   - Verify persistence after page reload

3. **Export Functionality**
   - After getting query results
   - Try exporting in different formats
   - Verify file downloads

4. **Filters**
   - Test company selection
   - Try date range filters
   - Verify filter application

## 🧪 **Automated Testing**

### **Backend Integration Test**
```bash
python test_integration_complete.py
```

### **Frontend E2E Test** (requires puppeteer)
```bash
cd frontend
npm install puppeteer  # if not installed
node test_phase6_comprehensive.js
```

## 🐛 **Troubleshooting**

### **Backend Issues**

**"Connection refused" errors:**
```bash
# Check if backend is running
curl http://127.0.0.1:8000/api/v1/health/

# If not running, start it:
cd backend
python -m uvicorn app.main:app --reload
```

**"Module not found" errors:**
```bash
cd backend
pip install -r requirements.txt
```

**Database/Vector issues:**
```bash
# Check if data is loaded
curl http://127.0.0.1:8000/api/v1/companies/
# Should return companies list
```

### **Frontend Issues**

**"Cannot connect to development server":**
```bash
cd frontend
npm install  # Install dependencies
npm run dev  # Start dev server
```

**"Module not found" errors:**
```bash
cd frontend
npm install  # Reinstall dependencies
```

**API connection errors:**
- Check if backend is running on port 8000
- Verify CORS is enabled in backend
- Check browser console for errors

### **Integration Issues**

**CORS Errors:**
- Backend should have CORS middleware enabled
- Check `app/main.py` for CORS configuration

**API Endpoint Mismatches:**
- Verify API base URL in frontend: `frontend/src/services/api.ts`
- Should be: `http://127.0.0.1:8000/api/v1`

## 📊 **Expected Test Results**

### **Successful Backend Test:**
```
✅ Backend Health: HEALTHY
✅ Companies API: WORKING (X companies)
✅ Filings API: WORKING (X filings)
✅ Query Engine: WORKING
✅ Cache System: WORKING
✅ System Status: WORKING
```

### **Successful Frontend Test:**
```
✅ Page Title: SEC Filing QA Agent
✅ Query Interface: FOUND
✅ Search Button: FOUND
✅ Sample Queries: X found
✅ Advanced Settings: EXPANDABLE
✅ Mobile Layout: RESPONSIVE
```

### **Successful Query Processing:**
```
✅ Query 1: SUCCESS (X.Xs)
  Success: true
  Model: gemma
  Sources: X
  Confidence: high/medium/low
  Answer: [Preview of answer]
```

## 🎯 **Test Scenarios**

### **Basic Functionality**
1. Load homepage ✅
2. Submit simple query ✅
3. View results with sources ✅
4. Test error handling ✅

### **Advanced Features**
1. Use advanced settings ✅
2. Test query history ✅
3. Bookmark queries ✅
4. Export results ✅
5. Apply filters ✅

### **Mobile Experience**
1. Responsive layout ✅
2. Touch interactions ✅
3. Mobile navigation ✅
4. FAB functionality ✅

### **Performance**
1. Query response time < 5s ✅
2. UI responsiveness ✅
3. Memory usage reasonable ✅
4. No console errors ✅

## 🚀 **Production Readiness Checklist**

- [ ] Backend API fully functional
- [ ] Frontend UI complete and responsive
- [ ] Query processing working end-to-end
- [ ] Error handling graceful
- [ ] Mobile experience optimized
- [ ] Performance acceptable
- [ ] No critical console errors
- [ ] Data persistence working
- [ ] Export functionality operational
- [ ] Advanced features functional

## 📞 **Support**

If you encounter issues:

1. **Check server status** - Both backend and frontend must be running
2. **Review console logs** - Browser console and terminal outputs
3. **Verify dependencies** - All packages installed correctly
4. **Test API directly** - Use http://127.0.0.1:8000/docs for API testing
5. **Clear browser cache** - Sometimes helps with frontend issues

## 🎉 **Success Indicators**

Your system is working correctly when:

✅ **Backend**: All API endpoints respond correctly
✅ **Frontend**: UI loads and is interactive
✅ **Integration**: Queries process and return results
✅ **Features**: Advanced features work as expected
✅ **Mobile**: Responsive design functions properly
✅ **Performance**: Response times are acceptable

**Happy Testing!** 🚀
