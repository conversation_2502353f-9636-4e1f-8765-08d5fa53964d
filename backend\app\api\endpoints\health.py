"""
Health check endpoints
"""

from fastapi import APIRouter
from datetime import datetime
import structlog

from app.core.config import settings

logger = structlog.get_logger()
router = APIRouter()


@router.get("/")
async def health_check():
    """
    Health check endpoint
    Returns system status and basic information
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "environment": settings.ENVIRONMENT,
        "version": "1.0.0"
    }


@router.get("/detailed")
async def detailed_health_check():
    """
    Detailed health check with component status
    """
    from app.services.vector_db import vector_db_manager

    components = {
        "api": "healthy",
        "vector_db": "checking",
        "openai": "not_implemented",
        "sec_api": "not_implemented",
        "redis": "not_implemented"
    }

    # Check vector database
    try:
        vector_health = await vector_db_manager.health_check()
        components["vector_db"] = vector_health.get("status", "error")
    except Exception as e:
        logger.error("Vector DB health check failed", error=str(e))
        components["vector_db"] = "error"

    # TODO: Add checks for:
    # - OpenAI API connectivity
    # - SEC API connectivity
    # - Redis connectivity

    overall_status = "healthy" if all(
        status in ["healthy", "not_implemented"]
        for status in components.values()
    ) else "unhealthy"

    return {
        "status": overall_status,
        "timestamp": datetime.utcnow().isoformat(),
        "environment": settings.ENVIRONMENT,
        "components": components
    }
