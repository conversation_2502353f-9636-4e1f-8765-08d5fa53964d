"""
Health check endpoints - Phase 5 Enhanced
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from datetime import datetime
from typing import Dict, Any, Optional
import structlog
import psutil
import os

from app.core.config import settings

logger = structlog.get_logger()
router = APIRouter()


class ComponentHealth(BaseModel):
    """Component health status model"""
    status: str = Field(..., description="Component status (healthy/unhealthy/degraded)")
    message: Optional[str] = Field(default=None, description="Status message")
    response_time_ms: Optional[float] = Field(default=None, description="Response time in milliseconds")
    last_check: str = Field(..., description="Last check timestamp")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Additional details")


class SystemHealth(BaseModel):
    """System health response model"""
    status: str = Field(..., description="Overall system status")
    timestamp: str = Field(..., description="Health check timestamp")
    environment: str = Field(..., description="Environment name")
    version: str = Field(..., description="API version")
    uptime_seconds: float = Field(..., description="System uptime in seconds")
    components: Dict[str, ComponentHealth] = Field(..., description="Component health status")
    system_metrics: Optional[Dict[str, Any]] = Field(default=None, description="System performance metrics")


@router.get("/", response_model=SystemHealth)
async def health_check():
    """
    Basic health check endpoint

    Returns overall system status and basic information.
    This is a lightweight check suitable for load balancers.
    """
    try:
        # Get basic system info
        uptime = _get_system_uptime()

        # Quick component checks
        components = await _check_basic_components()

        # Determine overall status
        overall_status = "healthy" if all(
            comp.status == "healthy" for comp in components.values()
        ) else "unhealthy"

        return SystemHealth(
            status=overall_status,
            timestamp=datetime.utcnow().isoformat(),
            environment=settings.ENVIRONMENT,
            version="1.0.0",
            uptime_seconds=uptime,
            components=components
        )

    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Health check failed")


@router.get("/detailed", response_model=SystemHealth)
async def detailed_health_check():
    """
    Comprehensive health check with detailed component status

    Returns detailed system health information including:
    - All component status with response times
    - System performance metrics
    - Service availability and configuration
    """
    try:
        start_time = datetime.utcnow()

        # Get system metrics
        uptime = _get_system_uptime()
        system_metrics = _get_system_metrics()

        # Comprehensive component checks
        components = await _check_all_components()

        # Determine overall status
        healthy_count = sum(1 for comp in components.values() if comp.status == "healthy")
        total_count = len(components)

        if healthy_count == total_count:
            overall_status = "healthy"
        elif healthy_count >= total_count * 0.7:  # 70% healthy
            overall_status = "degraded"
        else:
            overall_status = "unhealthy"

        logger.info("Detailed health check completed",
                   status=overall_status,
                   healthy_components=healthy_count,
                   total_components=total_count)

        return SystemHealth(
            status=overall_status,
            timestamp=datetime.utcnow().isoformat(),
            environment=settings.ENVIRONMENT,
            version="1.0.0",
            uptime_seconds=uptime,
            components=components,
            system_metrics=system_metrics
        )

    except Exception as e:
        logger.error("Detailed health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Detailed health check failed")


def _get_system_uptime() -> float:
    """Get system uptime in seconds"""
    try:
        return psutil.boot_time()
    except:
        return 0.0


def _get_system_metrics() -> Dict[str, Any]:
    """Get system performance metrics"""
    try:
        return {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent,
            "process_count": len(psutil.pids()),
            "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else [0, 0, 0]
        }
    except Exception as e:
        logger.error("Failed to get system metrics", error=str(e))
        return {}


async def _check_basic_components() -> Dict[str, ComponentHealth]:
    """Quick health checks for basic components"""
    components = {}
    check_time = datetime.utcnow().isoformat()

    # API component (always healthy if we reach here)
    components["api"] = ComponentHealth(
        status="healthy",
        message="API is responding",
        last_check=check_time
    )

    return components


async def _check_all_components() -> Dict[str, ComponentHealth]:
    """Comprehensive health checks for all components"""
    components = {}
    check_time = datetime.utcnow().isoformat()

    # API component
    components["api"] = ComponentHealth(
        status="healthy",
        message="API is responding",
        last_check=check_time
    )

    # Vector storage component
    components["vector_storage"] = await _check_vector_storage()

    # LLM service component
    components["llm_service"] = await _check_llm_service()

    # Query engine component
    components["query_engine"] = await _check_query_engine()

    # Embedding service component
    components["embedding_service"] = await _check_embedding_service()

    return components


async def _check_vector_storage() -> ComponentHealth:
    """Check vector storage health"""
    check_time = datetime.utcnow().isoformat()
    start_time = datetime.utcnow()

    try:
        from app.services.vector_storage import vector_storage

        stats = vector_storage.get_storage_stats()
        response_time = (datetime.utcnow() - start_time).total_seconds() * 1000

        if stats.get("faiss_vectors_count", 0) > 0:
            return ComponentHealth(
                status="healthy",
                message=f"Vector storage operational with {stats.get('faiss_vectors_count', 0)} vectors",
                response_time_ms=response_time,
                last_check=check_time,
                details=stats
            )
        else:
            return ComponentHealth(
                status="degraded",
                message="Vector storage operational but no vectors stored",
                response_time_ms=response_time,
                last_check=check_time,
                details=stats
            )

    except Exception as e:
        return ComponentHealth(
            status="unhealthy",
            message=f"Vector storage error: {str(e)}",
            last_check=check_time
        )


async def _check_llm_service() -> ComponentHealth:
    """Check LLM service health"""
    check_time = datetime.utcnow().isoformat()
    start_time = datetime.utcnow()

    try:
        from app.services.llm_service import llm_service

        status = llm_service.get_service_status()
        response_time = (datetime.utcnow() - start_time).total_seconds() * 1000

        if status.get("api_key_configured", False):
            return ComponentHealth(
                status="healthy",
                message=f"LLM service ready with {status.get('primary_model', 'unknown')} model",
                response_time_ms=response_time,
                last_check=check_time,
                details=status
            )
        else:
            return ComponentHealth(
                status="degraded",
                message="LLM service configured but API key missing",
                response_time_ms=response_time,
                last_check=check_time,
                details=status
            )

    except Exception as e:
        return ComponentHealth(
            status="unhealthy",
            message=f"LLM service error: {str(e)}",
            last_check=check_time
        )


async def _check_query_engine() -> ComponentHealth:
    """Check query engine health"""
    check_time = datetime.utcnow().isoformat()
    start_time = datetime.utcnow()

    try:
        from app.services.query_engine import query_engine

        status = query_engine.get_engine_status()
        response_time = (datetime.utcnow() - start_time).total_seconds() * 1000

        supported_companies = status.get("supported_companies", 0)
        if supported_companies > 0:
            return ComponentHealth(
                status="healthy",
                message=f"Query engine ready with {supported_companies} supported companies",
                response_time_ms=response_time,
                last_check=check_time,
                details=status
            )
        else:
            return ComponentHealth(
                status="degraded",
                message="Query engine operational but no companies supported",
                response_time_ms=response_time,
                last_check=check_time,
                details=status
            )

    except Exception as e:
        return ComponentHealth(
            status="unhealthy",
            message=f"Query engine error: {str(e)}",
            last_check=check_time
        )


async def _check_embedding_service() -> ComponentHealth:
    """Check embedding service health"""
    check_time = datetime.utcnow().isoformat()
    start_time = datetime.utcnow()

    try:
        from app.services.document_vectorizer import document_vectorizer

        status = document_vectorizer.get_pipeline_status()
        response_time = (datetime.utcnow() - start_time).total_seconds() * 1000

        embedding_service = status.get("embedding_service", {})
        current_service = embedding_service.get("current_service", "unknown")

        return ComponentHealth(
            status="healthy",
            message=f"Embedding service ready using {current_service}",
            response_time_ms=response_time,
            last_check=check_time,
            details=embedding_service
        )

    except Exception as e:
        return ComponentHealth(
            status="unhealthy",
            message=f"Embedding service error: {str(e)}",
            last_check=check_time
        )
