"use client";
import {
  require_createSvgIcon
} from "./chunk-55WFCA6X.js";
import "./chunk-5C26522B.js";
import "./chunk-GI6AN6AL.js";
import "./chunk-C2Y72P26.js";
import {
  require_interopRequireDefault
} from "./chunk-T4YASUMH.js";
import {
  require_jsx_runtime
} from "./chunk-GJN6C4Q3.js";
import {
  __commonJS
} from "./chunk-PUPCBSTA.js";

// node_modules/@mui/icons-material/TrendingUp.js
var require_TrendingUp = __commonJS({
  "node_modules/@mui/icons-material/TrendingUp.js"(exports) {
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _createSvgIcon = _interopRequireDefault(require_createSvgIcon());
    var _jsxRuntime = require_jsx_runtime();
    var _default = exports.default = (0, _createSvgIcon.default)((0, _jsxRuntime.jsx)("path", {
      d: "m16 6 2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"
    }), "TrendingUp");
  }
});
export default require_TrendingUp();
//# sourceMappingURL=@mui_icons-material_TrendingUp.js.map
