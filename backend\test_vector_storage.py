#!/usr/bin/env python3
"""
Test script for the Vector Storage Service
Tests Pinecone primary and FAISS local fallback
"""

import asyncio
import numpy as np
from app.services.vector_storage import vector_storage

async def test_vector_storage():
    """Test the vector storage service with various scenarios"""
    print("🗄️  VECTOR STORAGE SERVICE TESTS")
    print("=" * 50)
    
    # Test data - sample embeddings and metadata
    sample_embeddings = [
        np.random.rand(384).tolist(),  # Random 384-dimensional vectors
        np.random.rand(384).tolist(),
        np.random.rand(384).tolist(),
        np.random.rand(384).tolist(),
        np.random.rand(384).tolist()
    ]
    
    sample_metadata = [
        {
            "ticker": "AAPL",
            "filing_type": "10-Q",
            "accession_number": "0000320193-23-000064",
            "section_name": "financial_statements",
            "chunk_id": 0,
            "content": "Apple Inc. reported strong quarterly earnings...",
            "has_financial_data": True
        },
        {
            "ticker": "AAPL", 
            "filing_type": "10-Q",
            "accession_number": "0000320193-23-000064",
            "section_name": "risk_factors",
            "chunk_id": 1,
            "content": "Risk factors include supply chain disruptions...",
            "has_financial_data": False
        },
        {
            "ticker": "MSFT",
            "filing_type": "10-K",
            "accession_number": "0000789019-23-000456",
            "section_name": "business",
            "chunk_id": 0,
            "content": "Microsoft Corporation is a technology company...",
            "has_financial_data": False
        },
        {
            "ticker": "MSFT",
            "filing_type": "10-K", 
            "accession_number": "0000789019-23-000456",
            "section_name": "financial_statements",
            "chunk_id": 1,
            "content": "Revenue for fiscal year 2023 was $211.9 billion...",
            "has_financial_data": True
        },
        {
            "ticker": "GOOGL",
            "filing_type": "8-K",
            "accession_number": "0001652044-23-000789",
            "section_name": "material_events",
            "chunk_id": 0,
            "content": "Alphabet Inc. announces strategic acquisition...",
            "has_financial_data": False
        }
    ]
    
    print(f"📝 Test data: {len(sample_embeddings)} vectors with metadata")
    print()
    
    # Test 1: Service Status
    print("🔍 Test 1: Service Status")
    print("-" * 30)
    stats = vector_storage.get_storage_stats()
    print(f"  Primary Service: {stats['primary_service']}")
    print(f"  Current Service: {stats['current_service']}")
    print(f"  Fallback Active: {stats['fallback_active']}")
    print(f"  Vector Dimension: {stats['dimension']}")
    
    if stats['fallback_active']:
        print(f"  FAISS Vectors: {stats.get('faiss_vectors_count', 0)}")
        print(f"  FAISS Metadata: {stats.get('faiss_metadata_count', 0)}")
    else:
        print(f"  Pinecone Index: {stats.get('pinecone_index_name', 'N/A')}")
        print(f"  Pinecone Vectors: {stats.get('pinecone_vectors_count', 0)}")
    
    print()
    
    # Test 2: Store Vectors
    print("🔍 Test 2: Store Vectors")
    print("-" * 30)
    
    try:
        vector_ids = await vector_storage.store_vectors(sample_embeddings, sample_metadata)
        print(f"  ✅ Stored {len(vector_ids)} vectors")
        print(f"  📋 Vector IDs: {vector_ids[:2]}... (showing first 2)")
        
    except Exception as e:
        print(f"  ❌ Error storing vectors: {str(e)}")
        vector_ids = []
    
    print()
    
    # Test 3: Search Vectors
    print("🔍 Test 3: Search Vectors")
    print("-" * 30)
    
    if vector_ids:
        try:
            # Use first embedding as query
            query_vector = sample_embeddings[0]
            results = await vector_storage.search_vectors(query_vector, top_k=3)
            
            print(f"  ✅ Found {len(results)} similar vectors")
            for i, result in enumerate(results):
                print(f"    {i+1}. Score: {result['score']:.4f}, Ticker: {result['metadata'].get('ticker', 'N/A')}")
                print(f"       Filing: {result['metadata'].get('filing_type', 'N/A')}, Section: {result['metadata'].get('section_name', 'N/A')}")
            
        except Exception as e:
            print(f"  ❌ Error searching vectors: {str(e)}")
    else:
        print("  ⏭️  Skipping search test (no vectors stored)")
    
    print()
    
    # Test 4: Filtered Search
    print("🔍 Test 4: Filtered Search")
    print("-" * 30)
    
    if vector_ids:
        try:
            # Search for only AAPL vectors
            query_vector = sample_embeddings[0]
            filter_metadata = {"ticker": "AAPL"}
            results = await vector_storage.search_vectors(query_vector, top_k=5, filter_metadata=filter_metadata)
            
            print(f"  ✅ Found {len(results)} AAPL vectors")
            for i, result in enumerate(results):
                print(f"    {i+1}. Score: {result['score']:.4f}, Section: {result['metadata'].get('section_name', 'N/A')}")
            
        except Exception as e:
            print(f"  ❌ Error in filtered search: {str(e)}")
    else:
        print("  ⏭️  Skipping filtered search test (no vectors stored)")
    
    print()
    
    # Test 5: Storage Statistics After Operations
    print("🔍 Test 5: Storage Statistics After Operations")
    print("-" * 30)
    final_stats = vector_storage.get_storage_stats()
    print(f"  Current Service: {final_stats['current_service']}")
    print(f"  Fallback Active: {final_stats['fallback_active']}")
    
    if final_stats['fallback_active']:
        print(f"  FAISS Vectors: {final_stats.get('faiss_vectors_count', 0)}")
        print(f"  FAISS Metadata: {final_stats.get('faiss_metadata_count', 0)}")
        if final_stats.get('last_error'):
            print(f"  Last Error: {final_stats['last_error']}")
    else:
        print(f"  Pinecone Vectors: {final_stats.get('pinecone_vectors_count', 0)}")
    
    print()
    
    # Test 6: Empty Search
    print("🔍 Test 6: Empty Search Test")
    print("-" * 30)
    
    try:
        # Search with a random query vector
        random_query = np.random.rand(384).tolist()
        results = await vector_storage.search_vectors(random_query, top_k=1)
        print(f"  ✅ Random search returned {len(results)} results")
        
    except Exception as e:
        print(f"  ❌ Error in empty search: {str(e)}")
    
    print()
    
    # Test 7: Dimension Update Test
    print("🔍 Test 7: Dimension Update Test")
    print("-" * 30)
    
    try:
        original_dim = vector_storage.dimension
        print(f"  Original dimension: {original_dim}")
        
        # Test dimension update (this would normally be called by embedding service)
        vector_storage.update_dimension(1536)  # OpenAI dimension
        print(f"  Updated dimension: {vector_storage.dimension}")
        
        # Restore original dimension
        vector_storage.update_dimension(original_dim)
        print(f"  Restored dimension: {vector_storage.dimension}")
        
    except Exception as e:
        print(f"  ❌ Error in dimension update: {str(e)}")
    
    print()
    
    # Summary
    print("📊 TEST SUMMARY")
    print("=" * 50)
    final_stats = vector_storage.get_storage_stats()
    
    if final_stats['fallback_active']:
        print("  🟡 Using FAISS (local fallback)")
        if final_stats.get('last_error'):
            print(f"     Reason: {final_stats['last_error']}")
        print(f"  📊 Vectors Stored: {final_stats.get('faiss_vectors_count', 0)}")
        print(f"  💾 Storage Path: {final_stats.get('faiss_index_path', 'N/A')}")
    else:
        print("  🟢 Using Pinecone (primary)")
        print(f"  📊 Vectors Stored: {final_stats.get('pinecone_vectors_count', 0)}")
        print(f"  🏷️  Index Name: {final_stats.get('pinecone_index_name', 'N/A')}")
    
    print(f"  📏 Vector Dimension: {final_stats['dimension']}")
    print(f"  🔧 Service Ready: ✅")
    print()
    print("🎉 All vector storage tests completed!")

if __name__ == "__main__":
    asyncio.run(test_vector_storage())
